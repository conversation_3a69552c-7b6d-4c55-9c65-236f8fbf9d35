#ifndef __TB6612_H
#define __TB6612_H
#include "sys.h"

// 软件PWM配置参数
#define PWM_RESOLUTION          20      // PWM分辨率(0-19)
#define PWM_MAX_VALUE          19      // 电机PWM最大值 (与分辨率匹配)
#define PWM_FREQUENCY_HZ       50      // PWM输出频率50Hz
#define INTERRUPT_FREQUENCY_HZ 1000    // 中断频率1kHz

// 根据电路图修改引脚定义
// U3 TB6612
#define M1 PCout(5)   // AIN1
#define M2 PCout(4)   // AIN2
#define M3 PEout(7)   // BIN1
#define M4 PEout(8)   // BIN2

// U11 TB6612
#define M5 PDout(11)  // AIN1
#define M6 PDout(10)  // AIN2
#define M7 PDout(15)  // BIN1
#define M8 PCout(8)   // BIN2

void MOTOR_Init(void);
void TB6612_Init(int arr, int psc);
void Software_PWM_Init(void);
void motor_mode(void);
void Move_stop(void);

// 编码器相关函数声明
void Encoder_Init(void);           // 编码器初始化（仅TIM2）
int32_t Get_Encoder_Count(void);   // 获取编码器计数
void Reset_Encoder(void);          // 重置编码器计数

// PID控制相关定义
#define ENCODER_COUNTS_PER_REVOLUTION 1560  // 电机转一圈编码器计数值

// 电机编号定义
typedef enum {
    Motor1 = 1,
    Motor2 = 2,
    Motor3 = 3,
    Motor4 = 4
} MotorNum_t;

// PID控制器结构体
typedef struct {
    float Kp;           // 比例系数
    float Ki;           // 积分系数
    float Kd;           // 微分系数
    float target;       // 目标值
    float current;      // 当前值
    float error;        // 当前误差
    float last_error;   // 上次误差
    float integral;     // 积分累积
    float derivative;   // 微分值
    float output;       // 输出值
    float max_output;   // 最大输出限制
    float min_output;   // 最小输出限制
    float max_integral; // 积分限幅
} PID_Controller_t;

// PID控制相关函数声明
void PID_Init(PID_Controller_t *pid, float kp, float ki, float kd);
float PID_Calculate(PID_Controller_t *pid, float target, float current);
void Motor_SetDirection(MotorNum_t motor, int direction);
void Motor_SetPWM(MotorNum_t motor, int pwm);
void MotorPID_GO(MotorNum_t motor, float revolutions);

// PWM调试函数声明
uint8_t Motor_GetPWM(MotorNum_t motor);
uint8_t Get_PWM_Counter(void);

#endif
