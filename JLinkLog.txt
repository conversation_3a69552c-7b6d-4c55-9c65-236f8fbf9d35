T1378 000:007.017   SEGGER J-Link V6.84a Log File
T1378 000:007.119   DLL Compiled: Sep  7 2020 17:26:08
T1378 000:007.123   Logging started @ 2025-06-25 01:18
T1378 000:007.127 - 7.128ms
T1378 000:007.135 JLINK_SetWarnOutHandler(...)
T1378 000:007.165 - 0.032ms
T1378 000:007.170 JLINK_OpenEx(...)
T1378 000:009.704   Firmware: J-Link ARM-OB STM32 compiled Aug 22 2012 19:52:04
T1378 000:011.318   Hardware: V7.00
T1378 000:011.333   S/N: 20090928
T1378 000:011.339   OEM: SEGGER
T1378 000:011.344   Feature(s): jflash
T1378 000:012.037   TELNET listener socket opened on port 19021
T1378 000:012.179   WEBSRV Starting webserver
T1378 000:012.370   WEBSRV Webserver running on local port 19080
T1378 000:012.378 - 5.210ms returns "O.K."
T1378 000:012.395 JLINK_GetEmuCaps()
T1378 000:012.399 - 0.005ms returns 0x88EA5833
T1378 000:012.410 JLINK_TIF_GetAvailable(...)
T1378 000:012.553 - 0.151ms
T1378 000:012.566 JLINK_SetErrorOutHandler(...)
T1378 000:012.570 - 0.006ms
T1378 000:012.591 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\project\GCC\project_f103ve\JLinkSettings.ini"", ...). 
T1378 000:018.204   Ref file found at: C:\Keil_v5\ARM\Segger\JLinkDevices.ref
T1378 000:018.308   XML referenced by ref file: C:\Program Files (x86)\SEGGER\JLink\JLinkDevices.xml
T1378 000:018.794   C:\Program Files (x86)\SEGGER\JLink\JLinkDevices.xml evaluated successfully.
T1378 000:036.148 - 23.566ms returns 0x00
T1378 000:036.501 JLINK_ExecCommand("Device = STM32F103VE", ...). 
T1378 000:036.787   Device "STM32F103VE" selected.
T1378 000:037.089 - 0.580ms returns 0x00
T1378 000:037.103 JLINK_GetHardwareVersion()
T1378 000:037.107 - 0.006ms returns 70000
T1378 000:037.114 JLINK_GetDLLVersion()
T1378 000:037.118 - 0.005ms returns 68401
T1378 000:037.123 JLINK_GetOEMString(...)
T1378 000:037.128 JLINK_GetFirmwareString(...)
T1378 000:037.132 - 0.005ms
T1378 000:037.843 JLINK_GetDLLVersion()
T1378 000:037.851 - 0.009ms returns 68401
T1378 000:037.856 JLINK_GetCompileDateTime()
T1378 000:037.860 - 0.006ms
T1378 000:037.997 JLINK_GetFirmwareString(...)
T1378 000:038.002 - 0.006ms
T1378 000:038.123 JLINK_GetHardwareVersion()
T1378 000:038.127 - 0.006ms returns 70000
T1378 000:038.245 JLINK_GetSN()
T1378 000:038.250 - 0.006ms returns 20090928
T1378 000:038.368 JLINK_GetOEMString(...)
T1378 000:038.600 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T1378 000:038.910 - 0.316ms returns 0x00
T1378 000:038.923 JLINK_HasError()
T1378 000:038.931 JLINK_SetSpeed(5000)
T1378 000:038.983 - 0.055ms
T1378 000:038.989 JLINK_GetId()
T1378 000:039.864   Found SW-DP with ID 0x1BA01477
T1378 000:050.496   Found SW-DP with ID 0x1BA01477
T1378 000:052.472   Old FW that does not support reading DPIDR via DAP jobs
T1378 000:055.509   Unknown DP version. Assuming DPv0
T1378 000:055.633   Scanning AP map to find all available APs
T1378 000:057.463   AP[1]: Stopped AP scan as end of AP map has been reached
T1378 000:057.588   AP[0]: AHB-AP (IDR: 0x14770011)
T1378 000:057.704   Iterating through AP map to find AHB-AP to use
T1378 000:060.958   AP[0]: Core found
T1378 000:061.083   AP[0]: AHB-AP ROM base: 0xE00FF000
T1378 000:062.750   CPUID register: 0x411FC231. Implementer code: 0x41 (ARM)
T1378 000:062.872   Found Cortex-M3 r1p1, Little endian.
T1378 000:163.573   -- Max. mem block: 0x00002C18
T1378 000:163.607   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T1378 000:164.179   CPU_ReadMem(4 bytes @ 0xE0002000)
T1378 000:164.978   FPUnit: 6 code (BP) slots and 2 literal slots
T1378 000:164.993   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T1378 000:165.371   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T1378 000:165.739   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:166.109   CPU_WriteMem(4 bytes @ 0xE0001000)
T1378 000:166.523   CPU_ReadMem(4 bytes @ 0xE000ED88)
T1378 000:166.918   CPU_WriteMem(4 bytes @ 0xE000ED88)
T1378 000:167.346   CPU_ReadMem(4 bytes @ 0xE000ED88)
T1378 000:167.745   CPU_WriteMem(4 bytes @ 0xE000ED88)
T1378 000:168.601   CoreSight components:
T1378 000:168.779   ROMTbl[0] @ E00FF000
T1378 000:168.793   CPU_ReadMem(64 bytes @ 0xE00FF000)
T1378 000:169.818   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T1378 000:170.728   ROMTbl[0][0]: E000E000, CID: B105E00D, PID: 001BB000 SCS
T1378 000:170.743   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T1378 000:171.517   ROMTbl[0][1]: E0001000, CID: B105E00D, PID: 001BB002 DWT
T1378 000:171.528   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T1378 000:172.282   ROMTbl[0][2]: E0002000, CID: B105E00D, PID: 000BB003 FPB
T1378 000:172.291   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T1378 000:173.032   ROMTbl[0][3]: ********, CID: B105E00D, PID: 001BB001 ITM
T1378 000:173.041   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T1378 000:174.022   ROMTbl[0][4]: ********, CID: B105900D, PID: 001BB923 TPIU-Lite
T1378 000:174.044   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T1378 000:174.865   ROMTbl[0][5]: ********, CID: B105900D, PID: 101BB924 ETM-M3
T1378 000:175.199 - 136.213ms returns 0x1BA01477
T1378 000:175.216 JLINK_GetDLLVersion()
T1378 000:175.219 - 0.005ms returns 68401
T1378 000:175.226 JLINK_CORE_GetFound()
T1378 000:175.229 - 0.005ms returns 0x30000FF
T1378 000:175.271 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T1378 000:175.276   Value=0xE00FF000
T1378 000:175.281 - 0.012ms returns 0
T1378 000:175.421 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T1378 000:175.426   Value=0xE00FF000
T1378 000:175.431 - 0.012ms returns 0
T1378 000:175.435 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T1378 000:175.439   Value=0x********
T1378 000:175.444 - 0.010ms returns 0
T1378 000:175.449 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
T1378 000:175.465   CPU_ReadMem(32 bytes @ 0xE0041FD0)
T1378 000:176.087   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T1378 000:176.093 - 0.646ms returns 32 (0x20)
T1378 000:176.099 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T1378 000:176.102   Value=0x00000000
T1378 000:176.107 - 0.010ms returns 0
T1378 000:176.112 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T1378 000:176.115   Value=0x********
T1378 000:176.120 - 0.010ms returns 0
T1378 000:176.124 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T1378 000:176.128   Value=0x********
T1378 000:176.133 - 0.010ms returns 0
T1378 000:176.137 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T1378 000:176.140   Value=0xE0001000
T1378 000:176.145 - 0.010ms returns 0
T1378 000:176.149 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T1378 000:176.153   Value=0xE0002000
T1378 000:176.158 - 0.010ms returns 0
T1378 000:176.162 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T1378 000:176.166   Value=0xE000E000
T1378 000:176.170 - 0.010ms returns 0
T1378 000:176.175 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T1378 000:176.178   Value=0xE000EDF0
T1378 000:176.183 - 0.010ms returns 0
T1378 000:176.187 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T1378 000:176.193   CPU_ReadMem(4 bytes @ 0xE000ED00)
T1378 000:176.550   Data:  31 C2 1F 41
T1378 000:176.556   Debug reg: CPUID
T1378 000:176.561 - 0.375ms returns 1 (0x1)
T1378 000:176.566 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T1378 000:176.570   Value=0x00000000
T1378 000:176.575 - 0.010ms returns 0
T1378 000:176.579 JLINK_HasError()
T1378 000:176.584 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T1378 000:176.588 - 0.005ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T1378 000:176.593 JLINK_Reset()
T1378 000:176.603   CPU is running
T1378 000:176.609   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T1378 000:176.998   CPU is running
T1378 000:177.015   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T1378 000:177.593   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T1378 000:178.179   Reset: Reset device via AIRCR.SYSRESETREQ.
T1378 000:178.193   CPU is running
T1378 000:178.200   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T1378 000:230.617   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T1378 000:231.009   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T1378 000:231.387   CPU is running
T1378 000:231.399   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T1378 000:231.789   CPU is running
T1378 000:231.799   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T1378 000:237.485   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T1378 000:240.621   CPU_WriteMem(4 bytes @ 0xE0002000)
T1378 000:241.024   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T1378 000:241.385   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:241.742 - 65.153ms
T1378 000:241.769 JLINK_Halt()
T1378 000:241.774 - 0.007ms returns 0x00
T1378 000:241.781 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T1378 000:241.793   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T1378 000:242.167   Data:  03 00 03 00
T1378 000:242.185   Debug reg: DHCSR
T1378 000:242.193 - 0.414ms returns 1 (0x1)
T1378 000:242.216 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T1378 000:242.222   Debug reg: DHCSR
T1378 000:242.243   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T1378 000:242.644 - 0.436ms returns 0 (0x00000000)
T1378 000:242.663 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T1378 000:242.667   Debug reg: DEMCR
T1378 000:242.676   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T1378 000:243.075 - 0.421ms returns 0 (0x00000000)
T1378 000:243.693 JLINK_GetHWStatus(...)
T1378 000:243.808 - 0.121ms returns 0
T1378 000:244.198 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T1378 000:244.205 - 0.008ms returns 0x06
T1378 000:244.210 JLINK_GetNumBPUnits(Type = 0xF0)
T1378 000:244.213 - 0.005ms returns 0x2000
T1378 000:244.218 JLINK_GetNumWPUnits()
T1378 000:244.221 - 0.005ms returns 4
T1378 000:244.554 JLINK_GetSpeed()
T1378 000:244.558 - 0.006ms returns 4000
T1378 000:244.779 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T1378 000:244.788   CPU_ReadMem(4 bytes @ 0xE000E004)
T1378 000:245.147   Data:  01 00 00 00
T1378 000:245.153 - 0.375ms returns 1 (0x1)
T1378 000:245.158 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T1378 000:245.163   CPU_ReadMem(4 bytes @ 0xE000E004)
T1378 000:245.582   Data:  01 00 00 00
T1378 000:245.596 - 0.441ms returns 1 (0x1)
T1378 000:245.605 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T1378 000:245.611   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T1378 000:245.824   CPU_WriteMem(28 bytes @ 0xE0001000)
T1378 000:246.517 - 0.927ms returns 0x1C
T1378 000:246.551 JLINK_Halt()
T1378 000:246.558 - 0.009ms returns 0x00
T1378 000:246.565 JLINK_IsHalted()
T1378 000:246.571 - 0.008ms returns TRUE
T1378 000:247.699 JLINK_WriteMem(0x20000000, 0x164 Bytes, ...)
T1378 000:247.711   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T1378 000:247.912   CPU_WriteMem(356 bytes @ 0x20000000)
T1378 000:252.050 - 4.365ms returns 0x164
T1378 000:252.105 JLINK_HasError()
T1378 000:252.112 JLINK_WriteReg(R0, 0x08000000)
T1378 000:252.128 - 0.017ms returns 0
T1378 000:252.132 JLINK_WriteReg(R1, 0x00B71B00)
T1378 000:252.136 - 0.005ms returns 0
T1378 000:252.141 JLINK_WriteReg(R2, 0x00000001)
T1378 000:252.144 - 0.005ms returns 0
T1378 000:252.149 JLINK_WriteReg(R3, 0x00000000)
T1378 000:252.153 - 0.005ms returns 0
T1378 000:252.157 JLINK_WriteReg(R4, 0x00000000)
T1378 000:252.160 - 0.005ms returns 0
T1378 000:252.165 JLINK_WriteReg(R5, 0x00000000)
T1378 000:252.168 - 0.005ms returns 0
T1378 000:252.172 JLINK_WriteReg(R6, 0x00000000)
T1378 000:252.176 - 0.005ms returns 0
T1378 000:252.180 JLINK_WriteReg(R7, 0x00000000)
T1378 000:252.184 - 0.005ms returns 0
T1378 000:252.188 JLINK_WriteReg(R8, 0x00000000)
T1378 000:252.193 - 0.006ms returns 0
T1378 000:252.197 JLINK_WriteReg(R9, 0x20000160)
T1378 000:252.201 - 0.005ms returns 0
T1378 000:252.205 JLINK_WriteReg(R10, 0x00000000)
T1378 000:252.209 - 0.005ms returns 0
T1378 000:252.213 JLINK_WriteReg(R11, 0x00000000)
T1378 000:252.217 - 0.005ms returns 0
T1378 000:252.221 JLINK_WriteReg(R12, 0x00000000)
T1378 000:252.225 - 0.005ms returns 0
T1378 000:252.229 JLINK_WriteReg(R13 (SP), 0x20001000)
T1378 000:252.234 - 0.006ms returns 0
T1378 000:252.238 JLINK_WriteReg(R14, 0x20000001)
T1378 000:252.241 - 0.005ms returns 0
T1378 000:252.246 JLINK_WriteReg(R15 (PC), 0x20000038)
T1378 000:252.250 - 0.005ms returns 0
T1378 000:252.254 JLINK_WriteReg(XPSR, 0x01000000)
T1378 000:252.263 - 0.011ms returns 0
T1378 000:252.267 JLINK_WriteReg(MSP, 0x20001000)
T1378 000:252.271 - 0.005ms returns 0
T1378 000:252.275 JLINK_WriteReg(PSP, 0x20001000)
T1378 000:252.279 - 0.005ms returns 0
T1378 000:252.283 JLINK_WriteReg(CFBP, 0x00000000)
T1378 000:252.287 - 0.005ms returns 0
T1378 000:252.291 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T1378 000:252.300   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:252.688 - 0.408ms returns 0x00000001
T1378 000:252.707 JLINK_Go()
T1378 000:252.716   CPU_WriteMem(2 bytes @ 0x20000000)
T1378 000:253.146   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:253.550   CPU_WriteMem(4 bytes @ 0xE0002008)
T1378 000:253.567   CPU_WriteMem(4 bytes @ 0xE000200C)
T1378 000:253.575   CPU_WriteMem(4 bytes @ 0xE0002010)
T1378 000:253.582   CPU_WriteMem(4 bytes @ 0xE0002014)
T1378 000:253.589   CPU_WriteMem(4 bytes @ 0xE0002018)
T1378 000:253.596   CPU_WriteMem(4 bytes @ 0xE000201C)
T1378 000:255.639   CPU_WriteMem(4 bytes @ 0xE0001004)
T1378 000:258.625 - 5.928ms
T1378 000:258.643 JLINK_IsHalted()
T1378 000:261.495   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:261.894 - 3.260ms returns TRUE
T1378 000:261.910 JLINK_ReadReg(R15 (PC))
T1378 000:261.918 - 0.010ms returns 0x20000000
T1378 000:261.923 JLINK_ClrBPEx(BPHandle = 0x00000001)
T1378 000:261.927 - 0.006ms returns 0x00
T1378 000:261.932 JLINK_ReadReg(R0)
T1378 000:261.936 - 0.005ms returns 0x00000000
T1378 000:261.996 JLINK_HasError()
T1378 000:262.002 JLINK_WriteReg(R0, 0x08000000)
T1378 000:262.007 - 0.006ms returns 0
T1378 000:262.011 JLINK_WriteReg(R1, 0x00000400)
T1378 000:262.015 - 0.005ms returns 0
T1378 000:262.019 JLINK_WriteReg(R2, 0x000000FF)
T1378 000:262.023 - 0.005ms returns 0
T1378 000:262.027 JLINK_WriteReg(R3, 0x00000000)
T1378 000:262.031 - 0.005ms returns 0
T1378 000:262.035 JLINK_WriteReg(R4, 0x00000000)
T1378 000:262.039 - 0.005ms returns 0
T1378 000:262.043 JLINK_WriteReg(R5, 0x00000000)
T1378 000:262.047 - 0.006ms returns 0
T1378 000:262.052 JLINK_WriteReg(R6, 0x00000000)
T1378 000:262.056 - 0.005ms returns 0
T1378 000:262.060 JLINK_WriteReg(R7, 0x00000000)
T1378 000:262.064 - 0.005ms returns 0
T1378 000:262.068 JLINK_WriteReg(R8, 0x00000000)
T1378 000:262.072 - 0.005ms returns 0
T1378 000:262.076 JLINK_WriteReg(R9, 0x20000160)
T1378 000:262.079 - 0.005ms returns 0
T1378 000:262.084 JLINK_WriteReg(R10, 0x00000000)
T1378 000:262.087 - 0.005ms returns 0
T1378 000:262.092 JLINK_WriteReg(R11, 0x00000000)
T1378 000:262.095 - 0.005ms returns 0
T1378 000:262.099 JLINK_WriteReg(R12, 0x00000000)
T1378 000:262.103 - 0.005ms returns 0
T1378 000:262.107 JLINK_WriteReg(R13 (SP), 0x20001000)
T1378 000:262.111 - 0.005ms returns 0
T1378 000:262.116 JLINK_WriteReg(R14, 0x20000001)
T1378 000:262.119 - 0.005ms returns 0
T1378 000:262.124 JLINK_WriteReg(R15 (PC), 0x20000020)
T1378 000:262.128 - 0.005ms returns 0
T1378 000:262.132 JLINK_WriteReg(XPSR, 0x01000000)
T1378 000:262.136 - 0.005ms returns 0
T1378 000:262.140 JLINK_WriteReg(MSP, 0x20001000)
T1378 000:262.144 - 0.005ms returns 0
T1378 000:262.148 JLINK_WriteReg(PSP, 0x20001000)
T1378 000:262.152 - 0.005ms returns 0
T1378 000:262.156 JLINK_WriteReg(CFBP, 0x00000000)
T1378 000:262.160 - 0.005ms returns 0
T1378 000:262.164 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T1378 000:262.168 - 0.006ms returns 0x00000002
T1378 000:262.173 JLINK_Go()
T1378 000:262.182   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:265.255 - 3.096ms
T1378 000:265.279 JLINK_IsHalted()
T1378 000:268.082   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:268.464 - 3.188ms returns TRUE
T1378 000:268.479 JLINK_ReadReg(R15 (PC))
T1378 000:268.486 - 0.008ms returns 0x20000000
T1378 000:268.493 JLINK_ClrBPEx(BPHandle = 0x00000002)
T1378 000:268.497 - 0.006ms returns 0x00
T1378 000:268.502 JLINK_ReadReg(R0)
T1378 000:268.507 - 0.005ms returns 0x00000001
T1378 000:268.513 JLINK_HasError()
T1378 000:268.551 JLINK_WriteReg(R0, 0x08000000)
T1378 000:268.556 - 0.006ms returns 0
T1378 000:268.560 JLINK_WriteReg(R1, 0x00000400)
T1378 000:268.611 - 0.052ms returns 0
T1378 000:268.616 JLINK_WriteReg(R2, 0x000000FF)
T1378 000:268.619 - 0.005ms returns 0
T1378 000:268.624 JLINK_WriteReg(R3, 0x00000000)
T1378 000:268.628 - 0.005ms returns 0
T1378 000:268.632 JLINK_WriteReg(R4, 0x00000000)
T1378 000:268.636 - 0.005ms returns 0
T1378 000:268.640 JLINK_WriteReg(R5, 0x00000000)
T1378 000:268.644 - 0.005ms returns 0
T1378 000:268.648 JLINK_WriteReg(R6, 0x00000000)
T1378 000:268.651 - 0.005ms returns 0
T1378 000:268.656 JLINK_WriteReg(R7, 0x00000000)
T1378 000:268.659 - 0.005ms returns 0
T1378 000:268.664 JLINK_WriteReg(R8, 0x00000000)
T1378 000:268.667 - 0.005ms returns 0
T1378 000:268.672 JLINK_WriteReg(R9, 0x20000160)
T1378 000:268.675 - 0.005ms returns 0
T1378 000:268.680 JLINK_WriteReg(R10, 0x00000000)
T1378 000:268.683 - 0.005ms returns 0
T1378 000:268.688 JLINK_WriteReg(R11, 0x00000000)
T1378 000:268.691 - 0.005ms returns 0
T1378 000:268.696 JLINK_WriteReg(R12, 0x00000000)
T1378 000:268.699 - 0.005ms returns 0
T1378 000:268.704 JLINK_WriteReg(R13 (SP), 0x20001000)
T1378 000:268.708 - 0.005ms returns 0
T1378 000:268.712 JLINK_WriteReg(R14, 0x20000001)
T1378 000:268.716 - 0.005ms returns 0
T1378 000:268.720 JLINK_WriteReg(R15 (PC), 0x200000B6)
T1378 000:268.724 - 0.005ms returns 0
T1378 000:268.728 JLINK_WriteReg(XPSR, 0x01000000)
T1378 000:268.732 - 0.005ms returns 0
T1378 000:268.736 JLINK_WriteReg(MSP, 0x20001000)
T1378 000:268.740 - 0.005ms returns 0
T1378 000:268.744 JLINK_WriteReg(PSP, 0x20001000)
T1378 000:268.748 - 0.005ms returns 0
T1378 000:268.752 JLINK_WriteReg(CFBP, 0x00000000)
T1378 000:268.756 - 0.005ms returns 0
T1378 000:268.760 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T1378 000:268.765 - 0.006ms returns 0x00000003
T1378 000:268.769 JLINK_Go()
T1378 000:268.777   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:271.843 - 3.087ms
T1378 000:271.864 JLINK_IsHalted()
T1378 000:272.209 - 0.347ms returns FALSE
T1378 000:272.215 JLINK_HasError()
T1378 000:279.507 JLINK_IsHalted()
T1378 000:279.912 - 0.415ms returns FALSE
T1378 000:279.931 JLINK_HasError()
T1378 000:281.508 JLINK_IsHalted()
T1378 000:281.907 - 0.409ms returns FALSE
T1378 000:281.925 JLINK_HasError()
T1378 000:284.505 JLINK_IsHalted()
T1378 000:284.865 - 0.366ms returns FALSE
T1378 000:284.875 JLINK_HasError()
T1378 000:286.501 JLINK_IsHalted()
T1378 000:286.844 - 0.346ms returns FALSE
T1378 000:286.852 JLINK_HasError()
T1378 000:288.508 JLINK_IsHalted()
T1378 000:288.856 - 0.354ms returns FALSE
T1378 000:288.867 JLINK_HasError()
T1378 000:290.506 JLINK_IsHalted()
T1378 000:290.863 - 0.361ms returns FALSE
T1378 000:290.872 JLINK_HasError()
T1378 000:292.866 JLINK_IsHalted()
T1378 000:293.230 - 0.371ms returns FALSE
T1378 000:293.244 JLINK_HasError()
T1378 000:294.873 JLINK_IsHalted()
T1378 000:297.689   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:298.057 - 3.187ms returns TRUE
T1378 000:298.065 JLINK_ReadReg(R15 (PC))
T1378 000:298.072 - 0.009ms returns 0x20000000
T1378 000:298.079 JLINK_ClrBPEx(BPHandle = 0x00000003)
T1378 000:298.084 - 0.008ms returns 0x00
T1378 000:298.091 JLINK_ReadReg(R0)
T1378 000:298.096 - 0.007ms returns 0x00000000
T1378 000:298.133 JLINK_HasError()
T1378 000:298.140 JLINK_WriteReg(R0, 0x08000400)
T1378 000:298.146 - 0.008ms returns 0
T1378 000:298.151 JLINK_WriteReg(R1, 0x00000400)
T1378 000:298.157 - 0.007ms returns 0
T1378 000:298.163 JLINK_WriteReg(R2, 0x000000FF)
T1378 000:298.168 - 0.007ms returns 0
T1378 000:298.173 JLINK_WriteReg(R3, 0x00000000)
T1378 000:298.179 - 0.008ms returns 0
T1378 000:298.185 JLINK_WriteReg(R4, 0x00000000)
T1378 000:298.190 - 0.007ms returns 0
T1378 000:298.195 JLINK_WriteReg(R5, 0x00000000)
T1378 000:298.200 - 0.007ms returns 0
T1378 000:298.206 JLINK_WriteReg(R6, 0x00000000)
T1378 000:298.211 - 0.007ms returns 0
T1378 000:298.217 JLINK_WriteReg(R7, 0x00000000)
T1378 000:298.222 - 0.007ms returns 0
T1378 000:298.228 JLINK_WriteReg(R8, 0x00000000)
T1378 000:298.233 - 0.007ms returns 0
T1378 000:298.288 JLINK_WriteReg(R9, 0x20000160)
T1378 000:298.296 - 0.010ms returns 0
T1378 000:298.302 JLINK_WriteReg(R10, 0x00000000)
T1378 000:298.307 - 0.007ms returns 0
T1378 000:298.312 JLINK_WriteReg(R11, 0x00000000)
T1378 000:298.317 - 0.007ms returns 0
T1378 000:298.323 JLINK_WriteReg(R12, 0x00000000)
T1378 000:298.328 - 0.007ms returns 0
T1378 000:298.334 JLINK_WriteReg(R13 (SP), 0x20001000)
T1378 000:298.339 - 0.007ms returns 0
T1378 000:298.345 JLINK_WriteReg(R14, 0x20000001)
T1378 000:298.350 - 0.007ms returns 0
T1378 000:298.356 JLINK_WriteReg(R15 (PC), 0x20000020)
T1378 000:298.361 - 0.007ms returns 0
T1378 000:298.367 JLINK_WriteReg(XPSR, 0x01000000)
T1378 000:298.372 - 0.007ms returns 0
T1378 000:298.378 JLINK_WriteReg(MSP, 0x20001000)
T1378 000:298.383 - 0.007ms returns 0
T1378 000:298.388 JLINK_WriteReg(PSP, 0x20001000)
T1378 000:298.394 - 0.007ms returns 0
T1378 000:298.399 JLINK_WriteReg(CFBP, 0x00000000)
T1378 000:298.404 - 0.007ms returns 0
T1378 000:298.410 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T1378 000:298.416 - 0.008ms returns 0x00000004
T1378 000:298.422 JLINK_Go()
T1378 000:298.433   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:301.446 - 3.032ms
T1378 000:301.461 JLINK_IsHalted()
T1378 000:301.805 - 0.347ms returns FALSE
T1378 000:301.813 JLINK_HasError()
T1378 000:303.347 JLINK_IsHalted()
T1378 000:306.094   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:306.442 - 3.098ms returns TRUE
T1378 000:306.450 JLINK_ReadReg(R15 (PC))
T1378 000:306.456 - 0.008ms returns 0x20000000
T1378 000:306.462 JLINK_ClrBPEx(BPHandle = 0x00000004)
T1378 000:306.467 - 0.007ms returns 0x00
T1378 000:306.473 JLINK_ReadReg(R0)
T1378 000:306.478 - 0.007ms returns 0x00000000
T1378 000:306.498 JLINK_HasError()
T1378 000:306.504 JLINK_WriteReg(R0, 0x08000800)
T1378 000:306.510 - 0.007ms returns 0
T1378 000:306.515 JLINK_WriteReg(R1, 0x00000400)
T1378 000:306.520 - 0.007ms returns 0
T1378 000:306.526 JLINK_WriteReg(R2, 0x000000FF)
T1378 000:306.531 - 0.007ms returns 0
T1378 000:306.537 JLINK_WriteReg(R3, 0x00000000)
T1378 000:306.542 - 0.007ms returns 0
T1378 000:306.547 JLINK_WriteReg(R4, 0x00000000)
T1378 000:306.552 - 0.007ms returns 0
T1378 000:306.558 JLINK_WriteReg(R5, 0x00000000)
T1378 000:306.563 - 0.007ms returns 0
T1378 000:306.569 JLINK_WriteReg(R6, 0x00000000)
T1378 000:306.573 - 0.007ms returns 0
T1378 000:306.579 JLINK_WriteReg(R7, 0x00000000)
T1378 000:306.584 - 0.007ms returns 0
T1378 000:306.590 JLINK_WriteReg(R8, 0x00000000)
T1378 000:306.595 - 0.007ms returns 0
T1378 000:306.600 JLINK_WriteReg(R9, 0x20000160)
T1378 000:306.605 - 0.007ms returns 0
T1378 000:306.611 JLINK_WriteReg(R10, 0x00000000)
T1378 000:306.616 - 0.007ms returns 0
T1378 000:306.622 JLINK_WriteReg(R11, 0x00000000)
T1378 000:306.627 - 0.007ms returns 0
T1378 000:306.633 JLINK_WriteReg(R12, 0x00000000)
T1378 000:306.638 - 0.007ms returns 0
T1378 000:306.643 JLINK_WriteReg(R13 (SP), 0x20001000)
T1378 000:306.648 - 0.007ms returns 0
T1378 000:306.654 JLINK_WriteReg(R14, 0x20000001)
T1378 000:306.659 - 0.007ms returns 0
T1378 000:306.665 JLINK_WriteReg(R15 (PC), 0x20000020)
T1378 000:306.670 - 0.007ms returns 0
T1378 000:306.676 JLINK_WriteReg(XPSR, 0x01000000)
T1378 000:306.681 - 0.007ms returns 0
T1378 000:306.686 JLINK_WriteReg(MSP, 0x20001000)
T1378 000:306.691 - 0.007ms returns 0
T1378 000:306.697 JLINK_WriteReg(PSP, 0x20001000)
T1378 000:306.702 - 0.007ms returns 0
T1378 000:306.708 JLINK_WriteReg(CFBP, 0x00000000)
T1378 000:306.713 - 0.007ms returns 0
T1378 000:306.719 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T1378 000:306.724 - 0.007ms returns 0x00000005
T1378 000:306.730 JLINK_Go()
T1378 000:306.738   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:309.739 - 3.016ms
T1378 000:309.750 JLINK_IsHalted()
T1378 000:312.490   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:312.844 - 3.096ms returns TRUE
T1378 000:312.851 JLINK_ReadReg(R15 (PC))
T1378 000:312.858 - 0.008ms returns 0x20000000
T1378 000:312.864 JLINK_ClrBPEx(BPHandle = 0x00000005)
T1378 000:312.873 - 0.012ms returns 0x00
T1378 000:312.880 JLINK_ReadReg(R0)
T1378 000:312.885 - 0.007ms returns 0x00000001
T1378 000:312.891 JLINK_HasError()
T1378 000:312.897 JLINK_WriteReg(R0, 0x08000800)
T1378 000:312.902 - 0.007ms returns 0
T1378 000:312.908 JLINK_WriteReg(R1, 0x00000400)
T1378 000:312.913 - 0.007ms returns 0
T1378 000:312.919 JLINK_WriteReg(R2, 0x000000FF)
T1378 000:312.924 - 0.007ms returns 0
T1378 000:312.929 JLINK_WriteReg(R3, 0x00000000)
T1378 000:312.935 - 0.007ms returns 0
T1378 000:312.940 JLINK_WriteReg(R4, 0x00000000)
T1378 000:312.945 - 0.007ms returns 0
T1378 000:312.951 JLINK_WriteReg(R5, 0x00000000)
T1378 000:312.956 - 0.007ms returns 0
T1378 000:312.962 JLINK_WriteReg(R6, 0x00000000)
T1378 000:312.967 - 0.007ms returns 0
T1378 000:312.972 JLINK_WriteReg(R7, 0x00000000)
T1378 000:312.977 - 0.007ms returns 0
T1378 000:312.983 JLINK_WriteReg(R8, 0x00000000)
T1378 000:312.988 - 0.007ms returns 0
T1378 000:312.994 JLINK_WriteReg(R9, 0x20000160)
T1378 000:312.999 - 0.007ms returns 0
T1378 000:313.005 JLINK_WriteReg(R10, 0x00000000)
T1378 000:313.010 - 0.007ms returns 0
T1378 000:313.016 JLINK_WriteReg(R11, 0x00000000)
T1378 000:313.020 - 0.007ms returns 0
T1378 000:313.026 JLINK_WriteReg(R12, 0x00000000)
T1378 000:313.031 - 0.007ms returns 0
T1378 000:313.037 JLINK_WriteReg(R13 (SP), 0x20001000)
T1378 000:313.042 - 0.007ms returns 0
T1378 000:313.048 JLINK_WriteReg(R14, 0x20000001)
T1378 000:313.053 - 0.007ms returns 0
T1378 000:313.059 JLINK_WriteReg(R15 (PC), 0x200000B6)
T1378 000:313.064 - 0.007ms returns 0
T1378 000:313.070 JLINK_WriteReg(XPSR, 0x01000000)
T1378 000:313.075 - 0.007ms returns 0
T1378 000:313.081 JLINK_WriteReg(MSP, 0x20001000)
T1378 000:313.086 - 0.007ms returns 0
T1378 000:313.091 JLINK_WriteReg(PSP, 0x20001000)
T1378 000:313.096 - 0.007ms returns 0
T1378 000:313.102 JLINK_WriteReg(CFBP, 0x00000000)
T1378 000:313.107 - 0.007ms returns 0
T1378 000:313.113 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T1378 000:313.118 - 0.007ms returns 0x00000006
T1378 000:313.124 JLINK_Go()
T1378 000:313.132   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:316.126 - 3.010ms
T1378 000:316.140 JLINK_IsHalted()
T1378 000:316.475 - 0.338ms returns FALSE
T1378 000:316.482 JLINK_HasError()
T1378 000:317.526 JLINK_IsHalted()
T1378 000:317.861 - 0.338ms returns FALSE
T1378 000:317.868 JLINK_HasError()
T1378 000:319.573 JLINK_IsHalted()
T1378 000:319.933 - 0.367ms returns FALSE
T1378 000:319.955 JLINK_HasError()
T1378 000:321.569 JLINK_IsHalted()
T1378 000:321.940 - 0.374ms returns FALSE
T1378 000:321.947 JLINK_HasError()
T1378 000:323.574 JLINK_IsHalted()
T1378 000:323.929 - 0.358ms returns FALSE
T1378 000:323.936 JLINK_HasError()
T1378 000:325.570 JLINK_IsHalted()
T1378 000:325.931 - 0.368ms returns FALSE
T1378 000:325.945 JLINK_HasError()
T1378 000:327.568 JLINK_IsHalted()
T1378 000:327.939 - 0.374ms returns FALSE
T1378 000:327.947 JLINK_HasError()
T1378 000:329.568 JLINK_IsHalted()
T1378 000:329.932 - 0.366ms returns FALSE
T1378 000:329.939 JLINK_HasError()
T1378 000:331.573 JLINK_IsHalted()
T1378 000:331.934 - 0.367ms returns FALSE
T1378 000:331.947 JLINK_HasError()
T1378 000:333.574 JLINK_IsHalted()
T1378 000:333.946 - 0.374ms returns FALSE
T1378 000:333.953 JLINK_HasError()
T1378 000:335.945 JLINK_IsHalted()
T1378 000:336.338 - 0.395ms returns FALSE
T1378 000:336.345 JLINK_HasError()
T1378 000:337.979 JLINK_IsHalted()
T1378 000:340.789   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:341.173 - 3.197ms returns TRUE
T1378 000:341.183 JLINK_ReadReg(R15 (PC))
T1378 000:341.190 - 0.010ms returns 0x20000000
T1378 000:341.197 JLINK_ClrBPEx(BPHandle = 0x00000006)
T1378 000:341.203 - 0.008ms returns 0x00
T1378 000:341.209 JLINK_ReadReg(R0)
T1378 000:341.215 - 0.008ms returns 0x00000000
T1378 000:341.239 JLINK_HasError()
T1378 000:341.246 JLINK_WriteReg(R0, 0x08000C00)
T1378 000:341.252 - 0.008ms returns 0
T1378 000:341.259 JLINK_WriteReg(R1, 0x00000400)
T1378 000:341.264 - 0.008ms returns 0
T1378 000:341.274 JLINK_WriteReg(R2, 0x000000FF)
T1378 000:341.281 - 0.009ms returns 0
T1378 000:341.288 JLINK_WriteReg(R3, 0x00000000)
T1378 000:341.293 - 0.008ms returns 0
T1378 000:341.300 JLINK_WriteReg(R4, 0x00000000)
T1378 000:341.305 - 0.008ms returns 0
T1378 000:341.312 JLINK_WriteReg(R5, 0x00000000)
T1378 000:341.317 - 0.008ms returns 0
T1378 000:341.323 JLINK_WriteReg(R6, 0x00000000)
T1378 000:341.329 - 0.008ms returns 0
T1378 000:341.335 JLINK_WriteReg(R7, 0x00000000)
T1378 000:341.341 - 0.008ms returns 0
T1378 000:341.347 JLINK_WriteReg(R8, 0x00000000)
T1378 000:341.353 - 0.008ms returns 0
T1378 000:341.359 JLINK_WriteReg(R9, 0x20000160)
T1378 000:341.365 - 0.008ms returns 0
T1378 000:341.371 JLINK_WriteReg(R10, 0x00000000)
T1378 000:341.377 - 0.008ms returns 0
T1378 000:341.383 JLINK_WriteReg(R11, 0x00000000)
T1378 000:341.388 - 0.008ms returns 0
T1378 000:341.395 JLINK_WriteReg(R12, 0x00000000)
T1378 000:341.406 - 0.013ms returns 0
T1378 000:341.412 JLINK_WriteReg(R13 (SP), 0x20001000)
T1378 000:341.417 - 0.007ms returns 0
T1378 000:341.423 JLINK_WriteReg(R14, 0x20000001)
T1378 000:341.428 - 0.007ms returns 0
T1378 000:341.434 JLINK_WriteReg(R15 (PC), 0x20000020)
T1378 000:341.439 - 0.007ms returns 0
T1378 000:341.444 JLINK_WriteReg(XPSR, 0x01000000)
T1378 000:341.449 - 0.007ms returns 0
T1378 000:341.455 JLINK_WriteReg(MSP, 0x20001000)
T1378 000:341.460 - 0.007ms returns 0
T1378 000:341.466 JLINK_WriteReg(PSP, 0x20001000)
T1378 000:341.471 - 0.007ms returns 0
T1378 000:341.476 JLINK_WriteReg(CFBP, 0x00000000)
T1378 000:341.481 - 0.007ms returns 0
T1378 000:341.487 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T1378 000:341.492 - 0.007ms returns 0x00000007
T1378 000:341.498 JLINK_Go()
T1378 000:341.506   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:344.564 - 3.079ms
T1378 000:344.585 JLINK_IsHalted()
T1378 000:344.963 - 0.388ms returns FALSE
T1378 000:344.989 JLINK_HasError()
T1378 000:346.952 JLINK_IsHalted()
T1378 000:349.760   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:350.141 - 3.196ms returns TRUE
T1378 000:350.154 JLINK_ReadReg(R15 (PC))
T1378 000:350.163 - 0.011ms returns 0x20000000
T1378 000:350.169 JLINK_ClrBPEx(BPHandle = 0x00000007)
T1378 000:350.175 - 0.008ms returns 0x00
T1378 000:350.181 JLINK_ReadReg(R0)
T1378 000:350.186 - 0.007ms returns 0x00000000
T1378 000:350.219 JLINK_HasError()
T1378 000:350.225 JLINK_WriteReg(R0, 0x08001000)
T1378 000:350.231 - 0.007ms returns 0
T1378 000:350.237 JLINK_WriteReg(R1, 0x00000400)
T1378 000:350.242 - 0.007ms returns 0
T1378 000:350.247 JLINK_WriteReg(R2, 0x000000FF)
T1378 000:350.252 - 0.007ms returns 0
T1378 000:350.258 JLINK_WriteReg(R3, 0x00000000)
T1378 000:350.263 - 0.007ms returns 0
T1378 000:350.269 JLINK_WriteReg(R4, 0x00000000)
T1378 000:350.274 - 0.007ms returns 0
T1378 000:350.280 JLINK_WriteReg(R5, 0x00000000)
T1378 000:350.284 - 0.007ms returns 0
T1378 000:350.290 JLINK_WriteReg(R6, 0x00000000)
T1378 000:350.295 - 0.007ms returns 0
T1378 000:350.301 JLINK_WriteReg(R7, 0x00000000)
T1378 000:350.306 - 0.007ms returns 0
T1378 000:350.311 JLINK_WriteReg(R8, 0x00000000)
T1378 000:350.316 - 0.007ms returns 0
T1378 000:350.322 JLINK_WriteReg(R9, 0x20000160)
T1378 000:350.327 - 0.007ms returns 0
T1378 000:350.333 JLINK_WriteReg(R10, 0x00000000)
T1378 000:350.338 - 0.007ms returns 0
T1378 000:350.343 JLINK_WriteReg(R11, 0x00000000)
T1378 000:350.348 - 0.007ms returns 0
T1378 000:350.354 JLINK_WriteReg(R12, 0x00000000)
T1378 000:350.359 - 0.007ms returns 0
T1378 000:350.365 JLINK_WriteReg(R13 (SP), 0x20001000)
T1378 000:350.370 - 0.007ms returns 0
T1378 000:350.376 JLINK_WriteReg(R14, 0x20000001)
T1378 000:350.381 - 0.007ms returns 0
T1378 000:350.387 JLINK_WriteReg(R15 (PC), 0x20000020)
T1378 000:350.392 - 0.007ms returns 0
T1378 000:350.397 JLINK_WriteReg(XPSR, 0x01000000)
T1378 000:350.402 - 0.007ms returns 0
T1378 000:350.408 JLINK_WriteReg(MSP, 0x20001000)
T1378 000:350.413 - 0.007ms returns 0
T1378 000:350.419 JLINK_WriteReg(PSP, 0x20001000)
T1378 000:350.429 - 0.014ms returns 0
T1378 000:350.437 JLINK_WriteReg(CFBP, 0x00000000)
T1378 000:350.442 - 0.007ms returns 0
T1378 000:350.448 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T1378 000:350.453 - 0.007ms returns 0x00000008
T1378 000:350.459 JLINK_Go()
T1378 000:350.469   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:353.422 - 2.967ms
T1378 000:353.431 JLINK_IsHalted()
T1378 000:356.210   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:356.620 - 3.195ms returns TRUE
T1378 000:356.631 JLINK_ReadReg(R15 (PC))
T1378 000:356.638 - 0.009ms returns 0x20000000
T1378 000:356.643 JLINK_ClrBPEx(BPHandle = 0x00000008)
T1378 000:356.648 - 0.006ms returns 0x00
T1378 000:356.652 JLINK_ReadReg(R0)
T1378 000:356.656 - 0.005ms returns 0x00000001
T1378 000:356.661 JLINK_HasError()
T1378 000:356.666 JLINK_WriteReg(R0, 0x08001000)
T1378 000:356.670 - 0.006ms returns 0
T1378 000:356.674 JLINK_WriteReg(R1, 0x00000400)
T1378 000:356.678 - 0.005ms returns 0
T1378 000:356.682 JLINK_WriteReg(R2, 0x000000FF)
T1378 000:356.686 - 0.005ms returns 0
T1378 000:356.690 JLINK_WriteReg(R3, 0x00000000)
T1378 000:356.694 - 0.005ms returns 0
T1378 000:356.699 JLINK_WriteReg(R4, 0x00000000)
T1378 000:356.702 - 0.005ms returns 0
T1378 000:356.706 JLINK_WriteReg(R5, 0x00000000)
T1378 000:356.710 - 0.005ms returns 0
T1378 000:356.714 JLINK_WriteReg(R6, 0x00000000)
T1378 000:356.718 - 0.005ms returns 0
T1378 000:356.722 JLINK_WriteReg(R7, 0x00000000)
T1378 000:356.726 - 0.005ms returns 0
T1378 000:356.730 JLINK_WriteReg(R8, 0x00000000)
T1378 000:356.734 - 0.005ms returns 0
T1378 000:356.738 JLINK_WriteReg(R9, 0x20000160)
T1378 000:356.742 - 0.005ms returns 0
T1378 000:356.746 JLINK_WriteReg(R10, 0x00000000)
T1378 000:356.750 - 0.005ms returns 0
T1378 000:356.754 JLINK_WriteReg(R11, 0x00000000)
T1378 000:356.758 - 0.005ms returns 0
T1378 000:356.762 JLINK_WriteReg(R12, 0x00000000)
T1378 000:356.766 - 0.005ms returns 0
T1378 000:356.770 JLINK_WriteReg(R13 (SP), 0x20001000)
T1378 000:356.774 - 0.005ms returns 0
T1378 000:356.778 JLINK_WriteReg(R14, 0x20000001)
T1378 000:356.782 - 0.005ms returns 0
T1378 000:356.786 JLINK_WriteReg(R15 (PC), 0x200000B6)
T1378 000:356.790 - 0.005ms returns 0
T1378 000:356.794 JLINK_WriteReg(XPSR, 0x01000000)
T1378 000:356.798 - 0.005ms returns 0
T1378 000:356.802 JLINK_WriteReg(MSP, 0x20001000)
T1378 000:356.806 - 0.005ms returns 0
T1378 000:356.810 JLINK_WriteReg(PSP, 0x20001000)
T1378 000:356.814 - 0.005ms returns 0
T1378 000:356.818 JLINK_WriteReg(CFBP, 0x00000000)
T1378 000:356.822 - 0.005ms returns 0
T1378 000:356.827 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T1378 000:356.831 - 0.006ms returns 0x00000009
T1378 000:356.835 JLINK_Go()
T1378 000:356.843   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:359.891 - 3.060ms
T1378 000:359.901 JLINK_IsHalted()
T1378 000:360.249 - 0.350ms returns FALSE
T1378 000:360.256 JLINK_HasError()
T1378 000:361.896 JLINK_IsHalted()
T1378 000:362.341 - 0.452ms returns FALSE
T1378 000:362.354 JLINK_HasError()
T1378 000:364.423 JLINK_IsHalted()
T1378 000:364.779 - 0.359ms returns FALSE
T1378 000:364.787 JLINK_HasError()
T1378 000:366.397 JLINK_IsHalted()
T1378 000:366.734 - 0.340ms returns FALSE
T1378 000:366.741 JLINK_HasError()
T1378 000:368.399 JLINK_IsHalted()
T1378 000:368.763 - 0.371ms returns FALSE
T1378 000:368.775 JLINK_HasError()
T1378 000:370.400 JLINK_IsHalted()
T1378 000:370.769 - 0.371ms returns FALSE
T1378 000:370.780 JLINK_HasError()
T1378 000:372.397 JLINK_IsHalted()
T1378 000:372.759 - 0.364ms returns FALSE
T1378 000:372.766 JLINK_HasError()
T1378 000:374.399 JLINK_IsHalted()
T1378 000:374.747 - 0.355ms returns FALSE
T1378 000:374.760 JLINK_HasError()
T1378 000:376.398 JLINK_IsHalted()
T1378 000:376.736 - 0.341ms returns FALSE
T1378 000:376.743 JLINK_HasError()
T1378 000:378.735 JLINK_IsHalted()
T1378 000:379.098 - 0.365ms returns FALSE
T1378 000:379.105 JLINK_HasError()
T1378 000:380.741 JLINK_IsHalted()
T1378 000:381.104 - 0.370ms returns FALSE
T1378 000:381.117 JLINK_HasError()
T1378 000:382.742 JLINK_IsHalted()
T1378 000:385.566   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:385.978 - 3.247ms returns TRUE
T1378 000:385.997 JLINK_ReadReg(R15 (PC))
T1378 000:386.008 - 0.014ms returns 0x20000000
T1378 000:386.015 JLINK_ClrBPEx(BPHandle = 0x00000009)
T1378 000:386.022 - 0.009ms returns 0x00
T1378 000:386.028 JLINK_ReadReg(R0)
T1378 000:386.034 - 0.008ms returns 0x00000000
T1378 000:386.069 JLINK_HasError()
T1378 000:386.077 JLINK_WriteReg(R0, 0x08001400)
T1378 000:386.083 - 0.009ms returns 0
T1378 000:386.089 JLINK_WriteReg(R1, 0x00000400)
T1378 000:386.095 - 0.008ms returns 0
T1378 000:386.101 JLINK_WriteReg(R2, 0x000000FF)
T1378 000:386.107 - 0.008ms returns 0
T1378 000:386.113 JLINK_WriteReg(R3, 0x00000000)
T1378 000:386.119 - 0.008ms returns 0
T1378 000:386.125 JLINK_WriteReg(R4, 0x00000000)
T1378 000:386.130 - 0.008ms returns 0
T1378 000:386.137 JLINK_WriteReg(R5, 0x00000000)
T1378 000:386.142 - 0.008ms returns 0
T1378 000:386.148 JLINK_WriteReg(R6, 0x00000000)
T1378 000:386.154 - 0.008ms returns 0
T1378 000:386.160 JLINK_WriteReg(R7, 0x00000000)
T1378 000:386.166 - 0.008ms returns 0
T1378 000:386.172 JLINK_WriteReg(R8, 0x00000000)
T1378 000:386.178 - 0.008ms returns 0
T1378 000:386.184 JLINK_WriteReg(R9, 0x20000160)
T1378 000:386.190 - 0.008ms returns 0
T1378 000:386.196 JLINK_WriteReg(R10, 0x00000000)
T1378 000:386.202 - 0.008ms returns 0
T1378 000:386.208 JLINK_WriteReg(R11, 0x00000000)
T1378 000:386.213 - 0.008ms returns 0
T1378 000:386.220 JLINK_WriteReg(R12, 0x00000000)
T1378 000:386.225 - 0.008ms returns 0
T1378 000:386.232 JLINK_WriteReg(R13 (SP), 0x20001000)
T1378 000:386.237 - 0.008ms returns 0
T1378 000:386.244 JLINK_WriteReg(R14, 0x20000001)
T1378 000:386.249 - 0.008ms returns 0
T1378 000:386.256 JLINK_WriteReg(R15 (PC), 0x20000020)
T1378 000:386.261 - 0.008ms returns 0
T1378 000:386.268 JLINK_WriteReg(XPSR, 0x01000000)
T1378 000:386.273 - 0.008ms returns 0
T1378 000:386.280 JLINK_WriteReg(MSP, 0x20001000)
T1378 000:386.285 - 0.008ms returns 0
T1378 000:386.292 JLINK_WriteReg(PSP, 0x20001000)
T1378 000:386.297 - 0.008ms returns 0
T1378 000:386.304 JLINK_WriteReg(CFBP, 0x00000000)
T1378 000:386.309 - 0.008ms returns 0
T1378 000:386.316 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T1378 000:386.322 - 0.008ms returns 0x0000000A
T1378 000:386.328 JLINK_Go()
T1378 000:386.339   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:389.344 - 3.024ms
T1378 000:389.358 JLINK_IsHalted()
T1378 000:389.690 - 0.334ms returns FALSE
T1378 000:389.697 JLINK_HasError()
T1378 000:390.738 JLINK_IsHalted()
T1378 000:391.092 - 0.356ms returns FALSE
T1378 000:391.099 JLINK_HasError()
T1378 000:392.754 JLINK_IsHalted()
T1378 000:395.578   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:395.960 - 3.213ms returns TRUE
T1378 000:395.981 JLINK_ReadReg(R15 (PC))
T1378 000:395.989 - 0.010ms returns 0x20000000
T1378 000:395.997 JLINK_ClrBPEx(BPHandle = 0x0000000A)
T1378 000:396.003 - 0.008ms returns 0x00
T1378 000:396.009 JLINK_ReadReg(R0)
T1378 000:396.014 - 0.007ms returns 0x00000000
T1378 000:396.061 JLINK_HasError()
T1378 000:396.067 JLINK_WriteReg(R0, 0x00000001)
T1378 000:396.073 - 0.008ms returns 0
T1378 000:396.079 JLINK_WriteReg(R1, 0x00000400)
T1378 000:396.084 - 0.007ms returns 0
T1378 000:396.090 JLINK_WriteReg(R2, 0x000000FF)
T1378 000:396.095 - 0.007ms returns 0
T1378 000:396.100 JLINK_WriteReg(R3, 0x00000000)
T1378 000:396.105 - 0.007ms returns 0
T1378 000:396.111 JLINK_WriteReg(R4, 0x00000000)
T1378 000:396.116 - 0.007ms returns 0
T1378 000:396.122 JLINK_WriteReg(R5, 0x00000000)
T1378 000:396.127 - 0.007ms returns 0
T1378 000:396.132 JLINK_WriteReg(R6, 0x00000000)
T1378 000:396.137 - 0.007ms returns 0
T1378 000:396.143 JLINK_WriteReg(R7, 0x00000000)
T1378 000:396.148 - 0.007ms returns 0
T1378 000:396.154 JLINK_WriteReg(R8, 0x00000000)
T1378 000:396.159 - 0.007ms returns 0
T1378 000:396.164 JLINK_WriteReg(R9, 0x20000160)
T1378 000:396.169 - 0.007ms returns 0
T1378 000:396.175 JLINK_WriteReg(R10, 0x00000000)
T1378 000:396.185 - 0.014ms returns 0
T1378 000:396.193 JLINK_WriteReg(R11, 0x00000000)
T1378 000:396.198 - 0.007ms returns 0
T1378 000:396.204 JLINK_WriteReg(R12, 0x00000000)
T1378 000:396.209 - 0.007ms returns 0
T1378 000:396.214 JLINK_WriteReg(R13 (SP), 0x20001000)
T1378 000:396.220 - 0.008ms returns 0
T1378 000:396.226 JLINK_WriteReg(R14, 0x20000001)
T1378 000:396.231 - 0.007ms returns 0
T1378 000:396.236 JLINK_WriteReg(R15 (PC), 0x2000006A)
T1378 000:396.241 - 0.007ms returns 0
T1378 000:396.247 JLINK_WriteReg(XPSR, 0x01000000)
T1378 000:396.252 - 0.007ms returns 0
T1378 000:396.258 JLINK_WriteReg(MSP, 0x20001000)
T1378 000:396.263 - 0.007ms returns 0
T1378 000:396.269 JLINK_WriteReg(PSP, 0x20001000)
T1378 000:396.274 - 0.007ms returns 0
T1378 000:396.279 JLINK_WriteReg(CFBP, 0x00000000)
T1378 000:396.284 - 0.007ms returns 0
T1378 000:396.290 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T1378 000:396.296 - 0.008ms returns 0x0000000B
T1378 000:396.302 JLINK_Go()
T1378 000:396.311   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:399.346 - 3.053ms
T1378 000:399.362 JLINK_IsHalted()
T1378 000:402.124   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:402.497 - 3.138ms returns TRUE
T1378 000:402.505 JLINK_ReadReg(R15 (PC))
T1378 000:402.511 - 0.007ms returns 0x20000000
T1378 000:402.516 JLINK_ClrBPEx(BPHandle = 0x0000000B)
T1378 000:402.520 - 0.005ms returns 0x00
T1378 000:402.524 JLINK_ReadReg(R0)
T1378 000:402.528 - 0.005ms returns 0x00000000
T1378 000:453.827 JLINK_WriteMem(0x20000000, 0x164 Bytes, ...)
T1378 000:453.840   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T1378 000:453.862   CPU_WriteMem(356 bytes @ 0x20000000)
T1378 000:458.044 - 4.247ms returns 0x164
T1378 000:458.101 JLINK_HasError()
T1378 000:458.110 JLINK_WriteReg(R0, 0x08000000)
T1378 000:458.118 - 0.011ms returns 0
T1378 000:458.125 JLINK_WriteReg(R1, 0x00B71B00)
T1378 000:458.130 - 0.008ms returns 0
T1378 000:458.136 JLINK_WriteReg(R2, 0x00000002)
T1378 000:458.142 - 0.007ms returns 0
T1378 000:458.148 JLINK_WriteReg(R3, 0x00000000)
T1378 000:458.153 - 0.007ms returns 0
T1378 000:458.159 JLINK_WriteReg(R4, 0x00000000)
T1378 000:458.164 - 0.007ms returns 0
T1378 000:458.170 JLINK_WriteReg(R5, 0x00000000)
T1378 000:458.175 - 0.007ms returns 0
T1378 000:458.181 JLINK_WriteReg(R6, 0x00000000)
T1378 000:458.185 - 0.007ms returns 0
T1378 000:458.191 JLINK_WriteReg(R7, 0x00000000)
T1378 000:458.196 - 0.007ms returns 0
T1378 000:458.206 JLINK_WriteReg(R8, 0x00000000)
T1378 000:458.211 - 0.010ms returns 0
T1378 000:458.220 JLINK_WriteReg(R9, 0x20000160)
T1378 000:458.225 - 0.007ms returns 0
T1378 000:458.233 JLINK_WriteReg(R10, 0x00000000)
T1378 000:458.238 - 0.007ms returns 0
T1378 000:458.244 JLINK_WriteReg(R11, 0x00000000)
T1378 000:458.249 - 0.007ms returns 0
T1378 000:458.254 JLINK_WriteReg(R12, 0x00000000)
T1378 000:458.259 - 0.007ms returns 0
T1378 000:458.265 JLINK_WriteReg(R13 (SP), 0x20001000)
T1378 000:458.271 - 0.009ms returns 0
T1378 000:458.278 JLINK_WriteReg(R14, 0x20000001)
T1378 000:458.283 - 0.007ms returns 0
T1378 000:458.289 JLINK_WriteReg(R15 (PC), 0x20000038)
T1378 000:458.294 - 0.007ms returns 0
T1378 000:458.300 JLINK_WriteReg(XPSR, 0x01000000)
T1378 000:458.305 - 0.007ms returns 0
T1378 000:458.311 JLINK_WriteReg(MSP, 0x20001000)
T1378 000:458.316 - 0.007ms returns 0
T1378 000:458.321 JLINK_WriteReg(PSP, 0x20001000)
T1378 000:458.326 - 0.007ms returns 0
T1378 000:458.332 JLINK_WriteReg(CFBP, 0x00000000)
T1378 000:458.337 - 0.007ms returns 0
T1378 000:458.343 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T1378 000:458.352   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:458.801 - 0.470ms returns 0x0000000C
T1378 000:458.819 JLINK_Go()
T1378 000:458.828   CPU_WriteMem(2 bytes @ 0x20000000)
T1378 000:459.267   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:462.375 - 3.562ms
T1378 000:462.388 JLINK_IsHalted()
T1378 000:465.183   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:465.562 - 3.181ms returns TRUE
T1378 000:465.577 JLINK_ReadReg(R15 (PC))
T1378 000:465.590 - 0.017ms returns 0x20000000
T1378 000:465.598 JLINK_ClrBPEx(BPHandle = 0x0000000C)
T1378 000:465.605 - 0.008ms returns 0x00
T1378 000:465.611 JLINK_ReadReg(R0)
T1378 000:465.617 - 0.008ms returns 0x00000000
T1378 000:465.639 JLINK_WriteMem(0x20000164, 0x29C Bytes, ...)
T1378 000:465.645   Data:  A0 07 00 20 CD 01 00 08 D5 01 00 08 D7 01 00 08 ...
T1378 000:465.659   CPU_WriteMem(668 bytes @ 0x20000164)
T1378 000:472.968 - 7.337ms returns 0x29C
T1378 000:472.997 JLINK_WriteMem(0x20000400, 0x164 Bytes, ...)
T1378 000:473.004   Data:  8D F8 0E 00 03 A9 12 48 00 F0 24 F8 4F F6 FF 70 ...
T1378 000:473.018   CPU_WriteMem(356 bytes @ 0x20000400)
T1378 000:477.206 - 4.216ms returns 0x164
T1378 000:477.231 JLINK_HasError()
T1378 000:477.238 JLINK_WriteReg(R0, 0x08000000)
T1378 000:477.246 - 0.010ms returns 0
T1378 000:477.255 JLINK_WriteReg(R1, 0x00000400)
T1378 000:477.260 - 0.007ms returns 0
T1378 000:477.268 JLINK_WriteReg(R2, 0x20000164)
T1378 000:477.273 - 0.007ms returns 0
T1378 000:477.280 JLINK_WriteReg(R3, 0x00000000)
T1378 000:477.285 - 0.007ms returns 0
T1378 000:477.293 JLINK_WriteReg(R4, 0x00000000)
T1378 000:477.298 - 0.007ms returns 0
T1378 000:477.304 JLINK_WriteReg(R5, 0x00000000)
T1378 000:477.309 - 0.007ms returns 0
T1378 000:477.316 JLINK_WriteReg(R6, 0x00000000)
T1378 000:477.322 - 0.007ms returns 0
T1378 000:477.329 JLINK_WriteReg(R7, 0x00000000)
T1378 000:477.334 - 0.007ms returns 0
T1378 000:477.341 JLINK_WriteReg(R8, 0x00000000)
T1378 000:477.346 - 0.007ms returns 0
T1378 000:477.354 JLINK_WriteReg(R9, 0x20000160)
T1378 000:477.359 - 0.007ms returns 0
T1378 000:477.365 JLINK_WriteReg(R10, 0x00000000)
T1378 000:477.370 - 0.007ms returns 0
T1378 000:477.377 JLINK_WriteReg(R11, 0x00000000)
T1378 000:477.382 - 0.007ms returns 0
T1378 000:477.390 JLINK_WriteReg(R12, 0x00000000)
T1378 000:477.395 - 0.007ms returns 0
T1378 000:477.402 JLINK_WriteReg(R13 (SP), 0x20001000)
T1378 000:477.408 - 0.008ms returns 0
T1378 000:477.416 JLINK_WriteReg(R14, 0x20000001)
T1378 000:477.421 - 0.007ms returns 0
T1378 000:477.427 JLINK_WriteReg(R15 (PC), 0x200000F4)
T1378 000:477.432 - 0.007ms returns 0
T1378 000:477.439 JLINK_WriteReg(XPSR, 0x01000000)
T1378 000:477.445 - 0.007ms returns 0
T1378 000:477.452 JLINK_WriteReg(MSP, 0x20001000)
T1378 000:477.458 - 0.007ms returns 0
T1378 000:477.464 JLINK_WriteReg(PSP, 0x20001000)
T1378 000:477.469 - 0.007ms returns 0
T1378 000:477.477 JLINK_WriteReg(CFBP, 0x00000000)
T1378 000:477.482 - 0.007ms returns 0
T1378 000:477.488 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T1378 000:477.493 - 0.008ms returns 0x0000000D
T1378 000:477.501 JLINK_Go()
T1378 000:477.511   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:480.527 - 3.032ms
T1378 000:480.542 JLINK_IsHalted()
T1378 000:480.913 - 0.374ms returns FALSE
T1378 000:480.923 JLINK_HasError()
T1378 000:482.652 JLINK_IsHalted()
T1378 000:482.998 - 0.354ms returns FALSE
T1378 000:483.012 JLINK_HasError()
T1378 000:484.654 JLINK_IsHalted()
T1378 000:485.029 - 0.382ms returns FALSE
T1378 000:485.042 JLINK_HasError()
T1378 000:486.651 JLINK_IsHalted()
T1378 000:486.996 - 0.348ms returns FALSE
T1378 000:487.003 JLINK_HasError()
T1378 000:488.650 JLINK_IsHalted()
T1378 000:488.990 - 0.348ms returns FALSE
T1378 000:489.004 JLINK_HasError()
T1378 000:490.656 JLINK_IsHalted()
T1378 000:491.022 - 0.371ms returns FALSE
T1378 000:491.033 JLINK_HasError()
T1378 000:492.651 JLINK_IsHalted()
T1378 000:492.996 - 0.348ms returns FALSE
T1378 000:493.004 JLINK_HasError()
T1378 000:494.236 JLINK_IsHalted()
T1378 000:494.624 - 0.399ms returns FALSE
T1378 000:494.642 JLINK_HasError()
T1378 000:496.245 JLINK_IsHalted()
T1378 000:496.619 - 0.380ms returns FALSE
T1378 000:496.630 JLINK_HasError()
T1378 000:498.624 JLINK_IsHalted()
T1378 000:498.976 - 0.355ms returns FALSE
T1378 000:498.984 JLINK_HasError()
T1378 000:500.999 JLINK_IsHalted()
T1378 000:501.366 - 0.376ms returns FALSE
T1378 000:501.381 JLINK_HasError()
T1378 000:502.974 JLINK_IsHalted()
T1378 000:503.350 - 0.380ms returns FALSE
T1378 000:503.358 JLINK_HasError()
T1378 000:504.975 JLINK_IsHalted()
T1378 000:505.350 - 0.378ms returns FALSE
T1378 000:505.357 JLINK_HasError()
T1378 000:507.017 JLINK_IsHalted()
T1378 000:507.371 - 0.362ms returns FALSE
T1378 000:507.385 JLINK_HasError()
T1378 000:508.977 JLINK_IsHalted()
T1378 000:509.360 - 0.392ms returns FALSE
T1378 000:509.377 JLINK_HasError()
T1378 000:510.978 JLINK_IsHalted()
T1378 000:513.836   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:514.231 - 3.260ms returns TRUE
T1378 000:514.244 JLINK_ReadReg(R15 (PC))
T1378 000:514.253 - 0.011ms returns 0x20000000
T1378 000:514.259 JLINK_ClrBPEx(BPHandle = 0x0000000D)
T1378 000:514.265 - 0.008ms returns 0x00
T1378 000:514.271 JLINK_ReadReg(R0)
T1378 000:514.276 - 0.007ms returns 0x00000000
T1378 000:514.320 JLINK_WriteMem(0x20000164, 0x29C Bytes, ...)
T1378 000:514.326   Data:  54 60 BD E8 F0 81 41 61 70 47 01 61 70 47 0A B1 ...
T1378 000:514.339   CPU_WriteMem(668 bytes @ 0x20000164)
T1378 000:521.626 - 7.314ms returns 0x29C
T1378 000:521.641 JLINK_WriteMem(0x20000400, 0x164 Bytes, ...)
T1378 000:521.646   Data:  01 48 FF F7 B6 FE 08 BD 00 0C 01 40 70 B5 05 46 ...
T1378 000:521.659   CPU_WriteMem(356 bytes @ 0x20000400)
T1378 000:525.778 - 4.149ms returns 0x164
T1378 000:525.797 JLINK_HasError()
T1378 000:525.805 JLINK_WriteReg(R0, 0x08000400)
T1378 000:525.813 - 0.010ms returns 0
T1378 000:525.819 JLINK_WriteReg(R1, 0x00000400)
T1378 000:525.825 - 0.008ms returns 0
T1378 000:525.831 JLINK_WriteReg(R2, 0x20000164)
T1378 000:525.836 - 0.007ms returns 0
T1378 000:525.842 JLINK_WriteReg(R3, 0x00000000)
T1378 000:525.847 - 0.007ms returns 0
T1378 000:525.853 JLINK_WriteReg(R4, 0x00000000)
T1378 000:525.857 - 0.007ms returns 0
T1378 000:525.863 JLINK_WriteReg(R5, 0x00000000)
T1378 000:525.868 - 0.007ms returns 0
T1378 000:525.874 JLINK_WriteReg(R6, 0x00000000)
T1378 000:525.879 - 0.007ms returns 0
T1378 000:525.885 JLINK_WriteReg(R7, 0x00000000)
T1378 000:525.890 - 0.007ms returns 0
T1378 000:525.896 JLINK_WriteReg(R8, 0x00000000)
T1378 000:525.901 - 0.007ms returns 0
T1378 000:525.906 JLINK_WriteReg(R9, 0x20000160)
T1378 000:525.911 - 0.007ms returns 0
T1378 000:525.917 JLINK_WriteReg(R10, 0x00000000)
T1378 000:525.922 - 0.007ms returns 0
T1378 000:525.928 JLINK_WriteReg(R11, 0x00000000)
T1378 000:525.933 - 0.007ms returns 0
T1378 000:525.939 JLINK_WriteReg(R12, 0x00000000)
T1378 000:525.944 - 0.007ms returns 0
T1378 000:525.949 JLINK_WriteReg(R13 (SP), 0x20001000)
T1378 000:525.955 - 0.008ms returns 0
T1378 000:525.961 JLINK_WriteReg(R14, 0x20000001)
T1378 000:525.966 - 0.007ms returns 0
T1378 000:525.972 JLINK_WriteReg(R15 (PC), 0x200000F4)
T1378 000:525.977 - 0.007ms returns 0
T1378 000:525.982 JLINK_WriteReg(XPSR, 0x01000000)
T1378 000:525.988 - 0.007ms returns 0
T1378 000:525.993 JLINK_WriteReg(MSP, 0x20001000)
T1378 000:525.998 - 0.007ms returns 0
T1378 000:526.004 JLINK_WriteReg(PSP, 0x20001000)
T1378 000:526.009 - 0.007ms returns 0
T1378 000:526.015 JLINK_WriteReg(CFBP, 0x00000000)
T1378 000:526.020 - 0.007ms returns 0
T1378 000:526.026 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T1378 000:526.032 - 0.008ms returns 0x0000000E
T1378 000:526.038 JLINK_Go()
T1378 000:526.048   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:529.028 - 2.997ms
T1378 000:529.041 JLINK_IsHalted()
T1378 000:529.397 - 0.359ms returns FALSE
T1378 000:529.405 JLINK_HasError()
T1378 000:530.868 JLINK_IsHalted()
T1378 000:531.271 - 0.415ms returns FALSE
T1378 000:531.291 JLINK_HasError()
T1378 000:532.875 JLINK_IsHalted()
T1378 000:533.227 - 0.358ms returns FALSE
T1378 000:533.240 JLINK_HasError()
T1378 000:534.872 JLINK_IsHalted()
T1378 000:535.218 - 0.349ms returns FALSE
T1378 000:535.225 JLINK_HasError()
T1378 000:536.871 JLINK_IsHalted()
T1378 000:537.218 - 0.349ms returns FALSE
T1378 000:537.225 JLINK_HasError()
T1378 000:538.873 JLINK_IsHalted()
T1378 000:539.212 - 0.344ms returns FALSE
T1378 000:539.221 JLINK_HasError()
T1378 000:540.879 JLINK_IsHalted()
T1378 000:541.261 - 0.383ms returns FALSE
T1378 000:541.267 JLINK_HasError()
T1378 000:543.257 JLINK_IsHalted()
T1378 000:543.650 - 0.400ms returns FALSE
T1378 000:543.664 JLINK_HasError()
T1378 000:545.269 JLINK_IsHalted()
T1378 000:545.639 - 0.375ms returns FALSE
T1378 000:545.649 JLINK_HasError()
T1378 000:547.268 JLINK_IsHalted()
T1378 000:547.608 - 0.342ms returns FALSE
T1378 000:547.614 JLINK_HasError()
T1378 000:549.310 JLINK_IsHalted()
T1378 000:549.680 - 0.379ms returns FALSE
T1378 000:549.695 JLINK_HasError()
T1378 000:551.313 JLINK_IsHalted()
T1378 000:551.665 - 0.359ms returns FALSE
T1378 000:551.677 JLINK_HasError()
T1378 000:553.311 JLINK_IsHalted()
T1378 000:553.653 - 0.344ms returns FALSE
T1378 000:553.660 JLINK_HasError()
T1378 000:555.322 JLINK_IsHalted()
T1378 000:555.872 - 0.562ms returns FALSE
T1378 000:555.891 JLINK_HasError()
T1378 000:557.314 JLINK_IsHalted()
T1378 000:557.663 - 0.352ms returns FALSE
T1378 000:557.680 JLINK_HasError()
T1378 000:559.311 JLINK_IsHalted()
T1378 000:559.652 - 0.343ms returns FALSE
T1378 000:559.659 JLINK_HasError()
T1378 000:561.310 JLINK_IsHalted()
T1378 000:564.148   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:564.519 - 3.212ms returns TRUE
T1378 000:564.528 JLINK_ReadReg(R15 (PC))
T1378 000:564.536 - 0.010ms returns 0x20000000
T1378 000:564.542 JLINK_ClrBPEx(BPHandle = 0x0000000E)
T1378 000:564.548 - 0.008ms returns 0x00
T1378 000:564.554 JLINK_ReadReg(R0)
T1378 000:564.559 - 0.007ms returns 0x00000000
T1378 000:564.594 JLINK_WriteMem(0x20000164, 0x29C Bytes, ...)
T1378 000:564.600   Data:  FF F7 16 FF 70 BD 70 B5 05 46 0C 46 45 F0 B0 00 ...
T1378 000:564.611   CPU_WriteMem(668 bytes @ 0x20000164)
T1378 000:571.914 - 7.337ms returns 0x29C
T1378 000:571.944 JLINK_WriteMem(0x20000400, 0x164 Bytes, ...)
T1378 000:571.951   Data:  08 60 08 46 00 68 20 F4 80 20 08 60 08 46 40 68 ...
T1378 000:571.970   CPU_WriteMem(356 bytes @ 0x20000400)
T1378 000:576.126 - 4.195ms returns 0x164
T1378 000:576.147 JLINK_HasError()
T1378 000:576.153 JLINK_WriteReg(R0, 0x08000800)
T1378 000:576.162 - 0.011ms returns 0
T1378 000:576.167 JLINK_WriteReg(R1, 0x00000400)
T1378 000:576.171 - 0.005ms returns 0
T1378 000:576.176 JLINK_WriteReg(R2, 0x20000164)
T1378 000:576.179 - 0.005ms returns 0
T1378 000:576.184 JLINK_WriteReg(R3, 0x00000000)
T1378 000:576.187 - 0.005ms returns 0
T1378 000:576.192 JLINK_WriteReg(R4, 0x00000000)
T1378 000:576.195 - 0.005ms returns 0
T1378 000:576.199 JLINK_WriteReg(R5, 0x00000000)
T1378 000:576.204 - 0.005ms returns 0
T1378 000:576.208 JLINK_WriteReg(R6, 0x00000000)
T1378 000:576.211 - 0.005ms returns 0
T1378 000:576.216 JLINK_WriteReg(R7, 0x00000000)
T1378 000:576.219 - 0.005ms returns 0
T1378 000:576.224 JLINK_WriteReg(R8, 0x00000000)
T1378 000:576.227 - 0.005ms returns 0
T1378 000:576.232 JLINK_WriteReg(R9, 0x20000160)
T1378 000:576.235 - 0.005ms returns 0
T1378 000:576.240 JLINK_WriteReg(R10, 0x00000000)
T1378 000:576.243 - 0.005ms returns 0
T1378 000:576.248 JLINK_WriteReg(R11, 0x00000000)
T1378 000:576.251 - 0.005ms returns 0
T1378 000:576.256 JLINK_WriteReg(R12, 0x00000000)
T1378 000:576.259 - 0.005ms returns 0
T1378 000:576.264 JLINK_WriteReg(R13 (SP), 0x20001000)
T1378 000:576.268 - 0.006ms returns 0
T1378 000:576.272 JLINK_WriteReg(R14, 0x20000001)
T1378 000:576.276 - 0.005ms returns 0
T1378 000:576.280 JLINK_WriteReg(R15 (PC), 0x200000F4)
T1378 000:576.284 - 0.005ms returns 0
T1378 000:576.289 JLINK_WriteReg(XPSR, 0x01000000)
T1378 000:576.292 - 0.005ms returns 0
T1378 000:576.297 JLINK_WriteReg(MSP, 0x20001000)
T1378 000:576.300 - 0.005ms returns 0
T1378 000:576.305 JLINK_WriteReg(PSP, 0x20001000)
T1378 000:576.308 - 0.005ms returns 0
T1378 000:576.313 JLINK_WriteReg(CFBP, 0x00000000)
T1378 000:576.316 - 0.005ms returns 0
T1378 000:576.321 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T1378 000:576.326 - 0.006ms returns 0x0000000F
T1378 000:576.330 JLINK_Go()
T1378 000:576.341   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:579.358 - 3.036ms
T1378 000:579.374 JLINK_IsHalted()
T1378 000:579.794 - 0.434ms returns FALSE
T1378 000:579.816 JLINK_HasError()
T1378 000:581.557 JLINK_IsHalted()
T1378 000:581.983 - 0.433ms returns FALSE
T1378 000:581.997 JLINK_HasError()
T1378 000:583.553 JLINK_IsHalted()
T1378 000:583.902 - 0.351ms returns FALSE
T1378 000:583.909 JLINK_HasError()
T1378 000:585.903 JLINK_IsHalted()
T1378 000:586.359 - 0.465ms returns FALSE
T1378 000:586.375 JLINK_HasError()
T1378 000:587.900 JLINK_IsHalted()
T1378 000:588.270 - 0.372ms returns FALSE
T1378 000:588.276 JLINK_HasError()
T1378 000:589.900 JLINK_IsHalted()
T1378 000:590.235 - 0.337ms returns FALSE
T1378 000:590.242 JLINK_HasError()
T1378 000:591.905 JLINK_IsHalted()
T1378 000:592.272 - 0.375ms returns FALSE
T1378 000:592.289 JLINK_HasError()
T1378 000:593.904 JLINK_IsHalted()
T1378 000:594.282 - 0.383ms returns FALSE
T1378 000:594.292 JLINK_HasError()
T1378 000:595.900 JLINK_IsHalted()
T1378 000:596.239 - 0.341ms returns FALSE
T1378 000:596.246 JLINK_HasError()
T1378 000:597.939 JLINK_IsHalted()
T1378 000:598.364 - 0.433ms returns FALSE
T1378 000:598.379 JLINK_HasError()
T1378 000:599.900 JLINK_IsHalted()
T1378 000:600.271 - 0.373ms returns FALSE
T1378 000:600.276 JLINK_HasError()
T1378 000:601.907 JLINK_IsHalted()
T1378 000:602.258 - 0.353ms returns FALSE
T1378 000:602.266 JLINK_HasError()
T1378 000:604.259 JLINK_IsHalted()
T1378 000:604.624 - 0.372ms returns FALSE
T1378 000:604.637 JLINK_HasError()
T1378 000:606.259 JLINK_IsHalted()
T1378 000:606.614 - 0.357ms returns FALSE
T1378 000:606.619 JLINK_HasError()
T1378 000:608.265 JLINK_IsHalted()
T1378 000:608.656 - 0.394ms returns FALSE
T1378 000:608.669 JLINK_HasError()
T1378 000:610.262 JLINK_IsHalted()
T1378 000:613.040   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:613.426 - 3.167ms returns TRUE
T1378 000:613.434 JLINK_ReadReg(R15 (PC))
T1378 000:613.442 - 0.010ms returns 0x20000000
T1378 000:613.448 JLINK_ClrBPEx(BPHandle = 0x0000000F)
T1378 000:613.454 - 0.008ms returns 0x00
T1378 000:613.460 JLINK_ReadReg(R0)
T1378 000:613.466 - 0.007ms returns 0x00000000
T1378 000:613.502 JLINK_WriteMem(0x20000164, 0x29C Bytes, ...)
T1378 000:613.508   Data:  14 10 08 4A 52 F8 24 00 FF F7 FD FB 64 1C 04 2C ...
T1378 000:613.519   CPU_WriteMem(668 bytes @ 0x20000164)
T1378 000:620.798 - 7.305ms returns 0x29C
T1378 000:620.814 JLINK_WriteMem(0x20000400, 0x164 Bytes, ...)
T1378 000:620.819   Data:  10 BD 70 B5 02 46 00 24 00 23 00 25 00 20 40 F6 ...
T1378 000:620.832   CPU_WriteMem(356 bytes @ 0x20000400)
T1378 000:625.029 - 4.225ms returns 0x164
T1378 000:625.047 JLINK_HasError()
T1378 000:625.054 JLINK_WriteReg(R0, 0x08000C00)
T1378 000:625.062 - 0.011ms returns 0
T1378 000:625.068 JLINK_WriteReg(R1, 0x00000400)
T1378 000:625.074 - 0.007ms returns 0
T1378 000:625.080 JLINK_WriteReg(R2, 0x20000164)
T1378 000:625.085 - 0.007ms returns 0
T1378 000:625.090 JLINK_WriteReg(R3, 0x00000000)
T1378 000:625.095 - 0.007ms returns 0
T1378 000:625.101 JLINK_WriteReg(R4, 0x00000000)
T1378 000:625.106 - 0.007ms returns 0
T1378 000:625.112 JLINK_WriteReg(R5, 0x00000000)
T1378 000:625.117 - 0.007ms returns 0
T1378 000:625.122 JLINK_WriteReg(R6, 0x00000000)
T1378 000:625.127 - 0.007ms returns 0
T1378 000:625.133 JLINK_WriteReg(R7, 0x00000000)
T1378 000:625.138 - 0.007ms returns 0
T1378 000:625.143 JLINK_WriteReg(R8, 0x00000000)
T1378 000:625.149 - 0.007ms returns 0
T1378 000:625.154 JLINK_WriteReg(R9, 0x20000160)
T1378 000:625.159 - 0.007ms returns 0
T1378 000:625.165 JLINK_WriteReg(R10, 0x00000000)
T1378 000:625.170 - 0.007ms returns 0
T1378 000:625.176 JLINK_WriteReg(R11, 0x00000000)
T1378 000:625.181 - 0.007ms returns 0
T1378 000:625.186 JLINK_WriteReg(R12, 0x00000000)
T1378 000:625.191 - 0.007ms returns 0
T1378 000:625.197 JLINK_WriteReg(R13 (SP), 0x20001000)
T1378 000:625.203 - 0.008ms returns 0
T1378 000:625.208 JLINK_WriteReg(R14, 0x20000001)
T1378 000:625.213 - 0.007ms returns 0
T1378 000:625.219 JLINK_WriteReg(R15 (PC), 0x200000F4)
T1378 000:625.224 - 0.016ms returns 0
T1378 000:625.242 JLINK_WriteReg(XPSR, 0x01000000)
T1378 000:625.247 - 0.007ms returns 0
T1378 000:625.253 JLINK_WriteReg(MSP, 0x20001000)
T1378 000:625.258 - 0.007ms returns 0
T1378 000:625.263 JLINK_WriteReg(PSP, 0x20001000)
T1378 000:625.269 - 0.007ms returns 0
T1378 000:625.275 JLINK_WriteReg(CFBP, 0x00000000)
T1378 000:625.279 - 0.007ms returns 0
T1378 000:625.285 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T1378 000:625.291 - 0.008ms returns 0x00000010
T1378 000:625.297 JLINK_Go()
T1378 000:625.308   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:628.373 - 3.084ms
T1378 000:628.390 JLINK_IsHalted()
T1378 000:628.755 - 0.372ms returns FALSE
T1378 000:628.768 JLINK_HasError()
T1378 000:630.574 JLINK_IsHalted()
T1378 000:630.946 - 0.374ms returns FALSE
T1378 000:630.953 JLINK_HasError()
T1378 000:632.590 JLINK_IsHalted()
T1378 000:633.016 - 0.442ms returns FALSE
T1378 000:633.084 JLINK_HasError()
T1378 000:634.584 JLINK_IsHalted()
T1378 000:634.943 - 0.361ms returns FALSE
T1378 000:634.949 JLINK_HasError()
T1378 000:636.577 JLINK_IsHalted()
T1378 000:636.915 - 0.340ms returns FALSE
T1378 000:636.922 JLINK_HasError()
T1378 000:638.578 JLINK_IsHalted()
T1378 000:638.916 - 0.340ms returns FALSE
T1378 000:638.923 JLINK_HasError()
T1378 000:640.583 JLINK_IsHalted()
T1378 000:640.933 - 0.358ms returns FALSE
T1378 000:640.947 JLINK_HasError()
T1378 000:642.585 JLINK_IsHalted()
T1378 000:642.950 - 0.370ms returns FALSE
T1378 000:642.960 JLINK_HasError()
T1378 000:644.000 JLINK_IsHalted()
T1378 000:644.359 - 0.362ms returns FALSE
T1378 000:644.367 JLINK_HasError()
T1378 000:645.994 JLINK_IsHalted()
T1378 000:646.435 - 0.444ms returns FALSE
T1378 000:646.442 JLINK_HasError()
T1378 000:647.542 JLINK_IsHalted()
T1378 000:647.949 - 0.416ms returns FALSE
T1378 000:647.966 JLINK_HasError()
T1378 000:649.501 JLINK_IsHalted()
T1378 000:649.853 - 0.355ms returns FALSE
T1378 000:649.861 JLINK_HasError()
T1378 000:651.500 JLINK_IsHalted()
T1378 000:651.984 - 0.486ms returns FALSE
T1378 000:651.989 JLINK_HasError()
T1378 000:653.503 JLINK_IsHalted()
T1378 000:653.871 - 0.375ms returns FALSE
T1378 000:653.885 JLINK_HasError()
T1378 000:655.501 JLINK_IsHalted()
T1378 000:655.843 - 0.345ms returns FALSE
T1378 000:655.851 JLINK_HasError()
T1378 000:657.508 JLINK_IsHalted()
T1378 000:657.906 - 0.405ms returns FALSE
T1378 000:657.919 JLINK_HasError()
T1378 000:659.508 JLINK_IsHalted()
T1378 000:662.285   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:662.644 - 3.138ms returns TRUE
T1378 000:662.659 JLINK_ReadReg(R15 (PC))
T1378 000:662.666 - 0.010ms returns 0x20000000
T1378 000:662.674 JLINK_ClrBPEx(BPHandle = 0x00000010)
T1378 000:662.680 - 0.007ms returns 0x00
T1378 000:662.688 JLINK_ReadReg(R0)
T1378 000:662.693 - 0.007ms returns 0x00000000
T1378 000:662.734 JLINK_WriteMem(0x20000164, 0x29C Bytes, ...)
T1378 000:662.740   Data:  00 00 00 00 00 00 00 00 40 C0 78 40 C0 78 40 00 ...
T1378 000:662.752   CPU_WriteMem(668 bytes @ 0x20000164)
T1378 000:670.028 - 7.304ms returns 0x29C
T1378 000:670.045 JLINK_WriteMem(0x20000400, 0x164 Bytes, ...)
T1378 000:670.050   Data:  00 00 00 00 20 3F 20 20 20 20 30 00 08 F8 F8 00 ...
T1378 000:670.072   CPU_WriteMem(356 bytes @ 0x20000400)
T1378 000:674.203 - 4.166ms returns 0x164
T1378 000:674.218 JLINK_HasError()
T1378 000:674.225 JLINK_WriteReg(R0, 0x08001000)
T1378 000:674.233 - 0.010ms returns 0
T1378 000:674.239 JLINK_WriteReg(R1, 0x00000400)
T1378 000:674.245 - 0.007ms returns 0
T1378 000:674.251 JLINK_WriteReg(R2, 0x20000164)
T1378 000:674.256 - 0.007ms returns 0
T1378 000:674.261 JLINK_WriteReg(R3, 0x00000000)
T1378 000:674.266 - 0.007ms returns 0
T1378 000:674.272 JLINK_WriteReg(R4, 0x00000000)
T1378 000:674.277 - 0.007ms returns 0
T1378 000:674.282 JLINK_WriteReg(R5, 0x00000000)
T1378 000:674.287 - 0.007ms returns 0
T1378 000:674.293 JLINK_WriteReg(R6, 0x00000000)
T1378 000:674.298 - 0.007ms returns 0
T1378 000:674.304 JLINK_WriteReg(R7, 0x00000000)
T1378 000:674.309 - 0.007ms returns 0
T1378 000:674.360 JLINK_WriteReg(R8, 0x00000000)
T1378 000:674.368 - 0.011ms returns 0
T1378 000:674.374 JLINK_WriteReg(R9, 0x20000160)
T1378 000:674.379 - 0.007ms returns 0
T1378 000:674.385 JLINK_WriteReg(R10, 0x00000000)
T1378 000:674.390 - 0.007ms returns 0
T1378 000:674.396 JLINK_WriteReg(R11, 0x00000000)
T1378 000:674.401 - 0.007ms returns 0
T1378 000:674.406 JLINK_WriteReg(R12, 0x00000000)
T1378 000:674.411 - 0.007ms returns 0
T1378 000:674.417 JLINK_WriteReg(R13 (SP), 0x20001000)
T1378 000:674.422 - 0.007ms returns 0
T1378 000:674.428 JLINK_WriteReg(R14, 0x20000001)
T1378 000:674.433 - 0.007ms returns 0
T1378 000:674.439 JLINK_WriteReg(R15 (PC), 0x200000F4)
T1378 000:674.444 - 0.007ms returns 0
T1378 000:674.449 JLINK_WriteReg(XPSR, 0x01000000)
T1378 000:674.454 - 0.007ms returns 0
T1378 000:674.460 JLINK_WriteReg(MSP, 0x20001000)
T1378 000:674.465 - 0.007ms returns 0
T1378 000:674.471 JLINK_WriteReg(PSP, 0x20001000)
T1378 000:674.476 - 0.007ms returns 0
T1378 000:674.482 JLINK_WriteReg(CFBP, 0x00000000)
T1378 000:674.487 - 0.007ms returns 0
T1378 000:674.493 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T1378 000:674.499 - 0.008ms returns 0x00000011
T1378 000:674.505 JLINK_Go()
T1378 000:674.515   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:677.569 - 3.080ms
T1378 000:677.590 JLINK_IsHalted()
T1378 000:677.951 - 0.363ms returns FALSE
T1378 000:677.956 JLINK_HasError()
T1378 000:679.621 JLINK_IsHalted()
T1378 000:679.990 - 0.371ms returns FALSE
T1378 000:679.996 JLINK_HasError()
T1378 000:681.615 JLINK_IsHalted()
T1378 000:681.982 - 0.369ms returns FALSE
T1378 000:681.989 JLINK_HasError()
T1378 000:683.619 JLINK_IsHalted()
T1378 000:684.015 - 0.402ms returns FALSE
T1378 000:684.028 JLINK_HasError()
T1378 000:685.616 JLINK_IsHalted()
T1378 000:685.960 - 0.347ms returns FALSE
T1378 000:685.967 JLINK_HasError()
T1378 000:687.621 JLINK_IsHalted()
T1378 000:687.989 - 0.371ms returns FALSE
T1378 000:688.002 JLINK_HasError()
T1378 000:689.999 JLINK_IsHalted()
T1378 000:690.380 - 0.389ms returns FALSE
T1378 000:690.395 JLINK_HasError()
T1378 000:692.247 JLINK_IsHalted()
T1378 000:692.706 - 0.462ms returns FALSE
T1378 000:692.714 JLINK_HasError()
T1378 000:694.247 JLINK_IsHalted()
T1378 000:694.629 - 0.391ms returns FALSE
T1378 000:694.645 JLINK_HasError()
T1378 000:696.248 JLINK_IsHalted()
T1378 000:696.627 - 0.384ms returns FALSE
T1378 000:696.636 JLINK_HasError()
T1378 000:698.247 JLINK_IsHalted()
T1378 000:698.623 - 0.378ms returns FALSE
T1378 000:698.630 JLINK_HasError()
T1378 000:700.248 JLINK_IsHalted()
T1378 000:700.576 - 0.330ms returns FALSE
T1378 000:700.583 JLINK_HasError()
T1378 000:702.251 JLINK_IsHalted()
T1378 000:702.626 - 0.382ms returns FALSE
T1378 000:702.640 JLINK_HasError()
T1378 000:704.247 JLINK_IsHalted()
T1378 000:704.622 - 0.377ms returns FALSE
T1378 000:704.629 JLINK_HasError()
T1378 000:706.251 JLINK_IsHalted()
T1378 000:706.627 - 0.379ms returns FALSE
T1378 000:706.634 JLINK_HasError()
T1378 000:708.256 JLINK_IsHalted()
T1378 000:711.050   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:711.426 - 3.172ms returns TRUE
T1378 000:711.432 JLINK_ReadReg(R15 (PC))
T1378 000:711.438 - 0.007ms returns 0x20000000
T1378 000:711.443 JLINK_ClrBPEx(BPHandle = 0x00000011)
T1378 000:711.447 - 0.005ms returns 0x00
T1378 000:711.451 JLINK_ReadReg(R0)
T1378 000:711.455 - 0.005ms returns 0x00000000
T1378 000:711.499 JLINK_WriteMem(0x20000164, 0x29C Bytes, ...)
T1378 000:711.503   Data:  00 3F 11 20 20 11 0E 00 00 00 00 80 80 80 00 00 ...
T1378 000:711.512   CPU_WriteMem(668 bytes @ 0x20000164)
T1378 000:718.906 - 7.416ms returns 0x29C
T1378 000:718.930 JLINK_WriteMem(0x20000400, 0x164 Bytes, ...)
T1378 000:718.936   Data:  FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF ...
T1378 000:718.949   CPU_WriteMem(356 bytes @ 0x20000400)
T1378 000:723.123 - 4.202ms returns 0x164
T1378 000:723.146 JLINK_HasError()
T1378 000:723.154 JLINK_WriteReg(R0, 0x08001400)
T1378 000:723.162 - 0.010ms returns 0
T1378 000:723.168 JLINK_WriteReg(R1, 0x00000220)
T1378 000:723.218 - 0.055ms returns 0
T1378 000:723.229 JLINK_WriteReg(R2, 0x20000164)
T1378 000:723.235 - 0.007ms returns 0
T1378 000:723.240 JLINK_WriteReg(R3, 0x00000000)
T1378 000:723.245 - 0.007ms returns 0
T1378 000:723.251 JLINK_WriteReg(R4, 0x00000000)
T1378 000:723.256 - 0.007ms returns 0
T1378 000:723.263 JLINK_WriteReg(R5, 0x00000000)
T1378 000:723.268 - 0.007ms returns 0
T1378 000:723.276 JLINK_WriteReg(R6, 0x00000000)
T1378 000:723.281 - 0.007ms returns 0
T1378 000:723.288 JLINK_WriteReg(R7, 0x00000000)
T1378 000:723.293 - 0.007ms returns 0
T1378 000:723.300 JLINK_WriteReg(R8, 0x00000000)
T1378 000:723.306 - 0.007ms returns 0
T1378 000:723.311 JLINK_WriteReg(R9, 0x20000160)
T1378 000:723.316 - 0.007ms returns 0
T1378 000:723.324 JLINK_WriteReg(R10, 0x00000000)
T1378 000:723.329 - 0.007ms returns 0
T1378 000:723.337 JLINK_WriteReg(R11, 0x00000000)
T1378 000:723.342 - 0.007ms returns 0
T1378 000:723.350 JLINK_WriteReg(R12, 0x00000000)
T1378 000:723.355 - 0.007ms returns 0
T1378 000:723.360 JLINK_WriteReg(R13 (SP), 0x20001000)
T1378 000:723.366 - 0.008ms returns 0
T1378 000:723.374 JLINK_WriteReg(R14, 0x20000001)
T1378 000:723.379 - 0.007ms returns 0
T1378 000:723.387 JLINK_WriteReg(R15 (PC), 0x200000F4)
T1378 000:723.392 - 0.007ms returns 0
T1378 000:723.399 JLINK_WriteReg(XPSR, 0x01000000)
T1378 000:723.404 - 0.007ms returns 0
T1378 000:723.412 JLINK_WriteReg(MSP, 0x20001000)
T1378 000:723.417 - 0.007ms returns 0
T1378 000:723.425 JLINK_WriteReg(PSP, 0x20001000)
T1378 000:723.430 - 0.007ms returns 0
T1378 000:723.436 JLINK_WriteReg(CFBP, 0x00000000)
T1378 000:723.441 - 0.007ms returns 0
T1378 000:723.449 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T1378 000:723.455 - 0.008ms returns 0x00000012
T1378 000:723.463 JLINK_Go()
T1378 000:723.472   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:726.511 - 3.057ms
T1378 000:726.532 JLINK_IsHalted()
T1378 000:726.874 - 0.345ms returns FALSE
T1378 000:726.886 JLINK_HasError()
T1378 000:728.598 JLINK_IsHalted()
T1378 000:728.942 - 0.346ms returns FALSE
T1378 000:728.949 JLINK_HasError()
T1378 000:730.598 JLINK_IsHalted()
T1378 000:730.975 - 0.380ms returns FALSE
T1378 000:730.983 JLINK_HasError()
T1378 000:732.601 JLINK_IsHalted()
T1378 000:732.959 - 0.364ms returns FALSE
T1378 000:732.971 JLINK_HasError()
T1378 000:734.600 JLINK_IsHalted()
T1378 000:734.942 - 0.345ms returns FALSE
T1378 000:734.949 JLINK_HasError()
T1378 000:736.599 JLINK_IsHalted()
T1378 000:736.961 - 0.364ms returns FALSE
T1378 000:736.968 JLINK_HasError()
T1378 000:738.602 JLINK_IsHalted()
T1378 000:738.977 - 0.381ms returns FALSE
T1378 000:738.989 JLINK_HasError()
T1378 000:740.600 JLINK_IsHalted()
T1378 000:740.996 - 0.407ms returns FALSE
T1378 000:741.014 JLINK_HasError()
T1378 000:742.628 JLINK_IsHalted()
T1378 000:745.448   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:745.821 - 3.198ms returns TRUE
T1378 000:745.832 JLINK_ReadReg(R15 (PC))
T1378 000:745.839 - 0.008ms returns 0x20000000
T1378 000:745.844 JLINK_ClrBPEx(BPHandle = 0x00000012)
T1378 000:745.848 - 0.006ms returns 0x00
T1378 000:745.853 JLINK_ReadReg(R0)
T1378 000:745.857 - 0.005ms returns 0x00000000
T1378 000:745.862 JLINK_HasError()
T1378 000:745.867 JLINK_WriteReg(R0, 0x00000002)
T1378 000:745.871 - 0.006ms returns 0
T1378 000:745.876 JLINK_WriteReg(R1, 0x00000220)
T1378 000:745.880 - 0.005ms returns 0
T1378 000:745.884 JLINK_WriteReg(R2, 0x20000164)
T1378 000:745.888 - 0.005ms returns 0
T1378 000:745.893 JLINK_WriteReg(R3, 0x00000000)
T1378 000:745.896 - 0.005ms returns 0
T1378 000:745.901 JLINK_WriteReg(R4, 0x00000000)
T1378 000:745.904 - 0.005ms returns 0
T1378 000:745.909 JLINK_WriteReg(R5, 0x00000000)
T1378 000:745.912 - 0.005ms returns 0
T1378 000:745.917 JLINK_WriteReg(R6, 0x00000000)
T1378 000:745.920 - 0.005ms returns 0
T1378 000:745.925 JLINK_WriteReg(R7, 0x00000000)
T1378 000:745.928 - 0.005ms returns 0
T1378 000:745.933 JLINK_WriteReg(R8, 0x00000000)
T1378 000:745.937 - 0.005ms returns 0
T1378 000:745.941 JLINK_WriteReg(R9, 0x20000160)
T1378 000:745.982 - 0.043ms returns 0
T1378 000:745.987 JLINK_WriteReg(R10, 0x00000000)
T1378 000:745.990 - 0.005ms returns 0
T1378 000:745.995 JLINK_WriteReg(R11, 0x00000000)
T1378 000:745.998 - 0.005ms returns 0
T1378 000:746.003 JLINK_WriteReg(R12, 0x00000000)
T1378 000:746.006 - 0.005ms returns 0
T1378 000:746.011 JLINK_WriteReg(R13 (SP), 0x20001000)
T1378 000:746.015 - 0.005ms returns 0
T1378 000:746.019 JLINK_WriteReg(R14, 0x20000001)
T1378 000:746.023 - 0.005ms returns 0
T1378 000:746.027 JLINK_WriteReg(R15 (PC), 0x2000006A)
T1378 000:746.031 - 0.005ms returns 0
T1378 000:746.035 JLINK_WriteReg(XPSR, 0x01000000)
T1378 000:746.039 - 0.005ms returns 0
T1378 000:746.043 JLINK_WriteReg(MSP, 0x20001000)
T1378 000:746.047 - 0.005ms returns 0
T1378 000:746.052 JLINK_WriteReg(PSP, 0x20001000)
T1378 000:746.055 - 0.005ms returns 0
T1378 000:746.060 JLINK_WriteReg(CFBP, 0x00000000)
T1378 000:746.063 - 0.005ms returns 0
T1378 000:746.068 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T1378 000:746.072 - 0.006ms returns 0x00000013
T1378 000:746.077 JLINK_Go()
T1378 000:746.085   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:749.036 - 2.964ms
T1378 000:749.046 JLINK_IsHalted()
T1378 000:751.844   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:752.213 - 3.170ms returns TRUE
T1378 000:752.222 JLINK_ReadReg(R15 (PC))
T1378 000:752.229 - 0.009ms returns 0x20000000
T1378 000:752.235 JLINK_ClrBPEx(BPHandle = 0x00000013)
T1378 000:752.240 - 0.007ms returns 0x00
T1378 000:752.246 JLINK_ReadReg(R0)
T1378 000:752.251 - 0.007ms returns 0x00000000
T1378 000:804.460 JLINK_WriteMem(0x20000000, 0x164 Bytes, ...)
T1378 000:804.472   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T1378 000:804.491   CPU_WriteMem(356 bytes @ 0x20000000)
T1378 000:808.602 - 4.152ms returns 0x164
T1378 000:808.639 JLINK_HasError()
T1378 000:808.648 JLINK_WriteReg(R0, 0x08000000)
T1378 000:808.656 - 0.011ms returns 0
T1378 000:808.663 JLINK_WriteReg(R1, 0x00B71B00)
T1378 000:808.668 - 0.008ms returns 0
T1378 000:808.674 JLINK_WriteReg(R2, 0x00000003)
T1378 000:808.679 - 0.007ms returns 0
T1378 000:808.685 JLINK_WriteReg(R3, 0x00000000)
T1378 000:808.690 - 0.007ms returns 0
T1378 000:808.696 JLINK_WriteReg(R4, 0x00000000)
T1378 000:808.701 - 0.007ms returns 0
T1378 000:808.706 JLINK_WriteReg(R5, 0x00000000)
T1378 000:808.711 - 0.007ms returns 0
T1378 000:808.717 JLINK_WriteReg(R6, 0x00000000)
T1378 000:808.722 - 0.007ms returns 0
T1378 000:808.727 JLINK_WriteReg(R7, 0x00000000)
T1378 000:808.732 - 0.007ms returns 0
T1378 000:808.738 JLINK_WriteReg(R8, 0x00000000)
T1378 000:808.743 - 0.007ms returns 0
T1378 000:808.749 JLINK_WriteReg(R9, 0x20000160)
T1378 000:808.754 - 0.007ms returns 0
T1378 000:808.760 JLINK_WriteReg(R10, 0x00000000)
T1378 000:808.765 - 0.007ms returns 0
T1378 000:808.770 JLINK_WriteReg(R11, 0x00000000)
T1378 000:808.775 - 0.007ms returns 0
T1378 000:808.781 JLINK_WriteReg(R12, 0x00000000)
T1378 000:808.786 - 0.007ms returns 0
T1378 000:808.792 JLINK_WriteReg(R13 (SP), 0x20001000)
T1378 000:808.798 - 0.008ms returns 0
T1378 000:808.804 JLINK_WriteReg(R14, 0x20000001)
T1378 000:808.809 - 0.007ms returns 0
T1378 000:808.814 JLINK_WriteReg(R15 (PC), 0x20000038)
T1378 000:808.820 - 0.007ms returns 0
T1378 000:808.825 JLINK_WriteReg(XPSR, 0x01000000)
T1378 000:808.830 - 0.007ms returns 0
T1378 000:808.836 JLINK_WriteReg(MSP, 0x20001000)
T1378 000:808.841 - 0.007ms returns 0
T1378 000:808.847 JLINK_WriteReg(PSP, 0x20001000)
T1378 000:808.852 - 0.007ms returns 0
T1378 000:808.857 JLINK_WriteReg(CFBP, 0x00000000)
T1378 000:808.863 - 0.007ms returns 0
T1378 000:808.868 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T1378 000:808.877   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:809.255 - 0.389ms returns 0x00000014
T1378 000:809.263 JLINK_Go()
T1378 000:809.270   CPU_WriteMem(2 bytes @ 0x20000000)
T1378 000:809.672   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:812.770 - 3.515ms
T1378 000:812.784 JLINK_IsHalted()
T1378 000:815.590   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:816.138 - 3.367ms returns TRUE
T1378 000:816.158 JLINK_ReadReg(R15 (PC))
T1378 000:816.166 - 0.010ms returns 0x20000000
T1378 000:816.172 JLINK_ClrBPEx(BPHandle = 0x00000014)
T1378 000:816.177 - 0.007ms returns 0x00
T1378 000:816.184 JLINK_ReadReg(R0)
T1378 000:816.189 - 0.007ms returns 0x00000000
T1378 000:816.195 JLINK_HasError()
T1378 000:816.201 JLINK_WriteReg(R0, 0xFFFFFFFF)
T1378 000:816.207 - 0.007ms returns 0
T1378 000:816.212 JLINK_WriteReg(R1, 0x08000000)
T1378 000:816.217 - 0.007ms returns 0
T1378 000:816.223 JLINK_WriteReg(R2, 0x00001620)
T1378 000:816.228 - 0.007ms returns 0
T1378 000:816.234 JLINK_WriteReg(R3, 0x04C11DB7)
T1378 000:816.238 - 0.007ms returns 0
T1378 000:816.244 JLINK_WriteReg(R4, 0x00000000)
T1378 000:816.249 - 0.007ms returns 0
T1378 000:816.255 JLINK_WriteReg(R5, 0x00000000)
T1378 000:816.260 - 0.007ms returns 0
T1378 000:816.266 JLINK_WriteReg(R6, 0x00000000)
T1378 000:816.271 - 0.007ms returns 0
T1378 000:816.276 JLINK_WriteReg(R7, 0x00000000)
T1378 000:816.281 - 0.007ms returns 0
T1378 000:816.287 JLINK_WriteReg(R8, 0x00000000)
T1378 000:816.292 - 0.007ms returns 0
T1378 000:816.298 JLINK_WriteReg(R9, 0x20000160)
T1378 000:816.303 - 0.007ms returns 0
T1378 000:816.308 JLINK_WriteReg(R10, 0x00000000)
T1378 000:816.313 - 0.007ms returns 0
T1378 000:816.319 JLINK_WriteReg(R11, 0x00000000)
T1378 000:816.324 - 0.007ms returns 0
T1378 000:816.330 JLINK_WriteReg(R12, 0x00000000)
T1378 000:816.335 - 0.007ms returns 0
T1378 000:816.340 JLINK_WriteReg(R13 (SP), 0x20001000)
T1378 000:816.345 - 0.007ms returns 0
T1378 000:816.351 JLINK_WriteReg(R14, 0x20000001)
T1378 000:816.356 - 0.007ms returns 0
T1378 000:816.362 JLINK_WriteReg(R15 (PC), 0x20000002)
T1378 000:816.367 - 0.007ms returns 0
T1378 000:816.372 JLINK_WriteReg(XPSR, 0x01000000)
T1378 000:816.377 - 0.007ms returns 0
T1378 000:816.383 JLINK_WriteReg(MSP, 0x20001000)
T1378 000:816.388 - 0.007ms returns 0
T1378 000:816.394 JLINK_WriteReg(PSP, 0x20001000)
T1378 000:816.399 - 0.007ms returns 0
T1378 000:816.404 JLINK_WriteReg(CFBP, 0x00000000)
T1378 000:816.409 - 0.007ms returns 0
T1378 000:816.415 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T1378 000:816.420 - 0.007ms returns 0x00000015
T1378 000:816.426 JLINK_Go()
T1378 000:816.435   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:819.469 - 3.052ms
T1378 000:819.484 JLINK_IsHalted()
T1378 000:819.853 - 0.372ms returns FALSE
T1378 000:819.861 JLINK_HasError()
T1378 000:821.791 JLINK_IsHalted()
T1378 000:822.208 - 0.428ms returns FALSE
T1378 000:822.226 JLINK_HasError()
T1378 000:823.795 JLINK_IsHalted()
T1378 000:824.165 - 0.373ms returns FALSE
T1378 000:824.173 JLINK_HasError()
T1378 000:825.790 JLINK_IsHalted()
T1378 000:826.152 - 0.364ms returns FALSE
T1378 000:826.159 JLINK_HasError()
T1378 000:827.791 JLINK_IsHalted()
T1378 000:828.268 - 0.484ms returns FALSE
T1378 000:828.292 JLINK_HasError()
T1378 000:829.792 JLINK_IsHalted()
T1378 000:830.163 - 0.374ms returns FALSE
T1378 000:830.172 JLINK_HasError()
T1378 000:831.793 JLINK_IsHalted()
T1378 000:832.167 - 0.376ms returns FALSE
T1378 000:832.174 JLINK_HasError()
T1378 000:833.892 JLINK_IsHalted()
T1378 000:834.373 - 0.494ms returns FALSE
T1378 000:834.392 JLINK_HasError()
T1378 000:835.896 JLINK_IsHalted()
T1378 000:836.260 - 0.367ms returns FALSE
T1378 000:836.267 JLINK_HasError()
T1378 000:837.899 JLINK_IsHalted()
T1378 000:838.258 - 0.362ms returns FALSE
T1378 000:838.265 JLINK_HasError()
T1378 000:839.891 JLINK_IsHalted()
T1378 000:840.398 - 0.514ms returns FALSE
T1378 000:840.412 JLINK_HasError()
T1378 000:841.899 JLINK_IsHalted()
T1378 000:842.265 - 0.371ms returns FALSE
T1378 000:842.275 JLINK_HasError()
T1378 000:843.889 JLINK_IsHalted()
T1378 000:844.225 - 0.338ms returns FALSE
T1378 000:844.231 JLINK_HasError()
T1378 000:845.900 JLINK_IsHalted()
T1378 000:846.276 - 0.378ms returns FALSE
T1378 000:846.283 JLINK_HasError()
T1378 000:847.402 JLINK_IsHalted()
T1378 000:847.776 - 0.422ms returns FALSE
T1378 000:847.832 JLINK_HasError()
T1378 000:849.407 JLINK_IsHalted()
T1378 000:849.776 - 0.372ms returns FALSE
T1378 000:849.784 JLINK_HasError()
T1378 000:851.414 JLINK_IsHalted()
T1378 000:851.793 - 0.384ms returns FALSE
T1378 000:851.802 JLINK_HasError()
T1378 000:853.409 JLINK_IsHalted()
T1378 000:853.787 - 0.385ms returns FALSE
T1378 000:853.807 JLINK_HasError()
T1378 000:855.787 JLINK_IsHalted()
T1378 000:856.152 - 0.367ms returns FALSE
T1378 000:856.159 JLINK_HasError()
T1378 000:857.786 JLINK_IsHalted()
T1378 000:858.162 - 0.378ms returns FALSE
T1378 000:858.169 JLINK_HasError()
T1378 000:859.793 JLINK_IsHalted()
T1378 000:860.173 - 0.387ms returns FALSE
T1378 000:860.187 JLINK_HasError()
T1378 000:861.788 JLINK_IsHalted()
T1378 000:862.157 - 0.372ms returns FALSE
T1378 000:862.165 JLINK_HasError()
T1378 000:863.883 JLINK_IsHalted()
T1378 000:864.274 - 0.393ms returns FALSE
T1378 000:864.281 JLINK_HasError()
T1378 000:865.886 JLINK_IsHalted()
T1378 000:866.251 - 0.373ms returns FALSE
T1378 000:866.266 JLINK_HasError()
T1378 000:867.883 JLINK_IsHalted()
T1378 000:868.235 - 0.353ms returns FALSE
T1378 000:868.240 JLINK_HasError()
T1378 000:869.883 JLINK_IsHalted()
T1378 000:870.376 - 0.495ms returns FALSE
T1378 000:870.382 JLINK_HasError()
T1378 000:871.887 JLINK_IsHalted()
T1378 000:872.262 - 0.380ms returns FALSE
T1378 000:872.271 JLINK_HasError()
T1378 000:873.883 JLINK_IsHalted()
T1378 000:874.242 - 0.362ms returns FALSE
T1378 000:874.249 JLINK_HasError()
T1378 000:876.387 JLINK_IsHalted()
T1378 000:876.757 - 0.377ms returns FALSE
T1378 000:876.771 JLINK_HasError()
T1378 000:878.392 JLINK_IsHalted()
T1378 000:878.823 - 0.434ms returns FALSE
T1378 000:878.831 JLINK_HasError()
T1378 000:880.390 JLINK_IsHalted()
T1378 000:880.781 - 0.394ms returns FALSE
T1378 000:880.788 JLINK_HasError()
T1378 000:882.410 JLINK_IsHalted()
T1378 000:885.309   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:885.736 - 3.333ms returns TRUE
T1378 000:885.751 JLINK_ReadReg(R15 (PC))
T1378 000:885.761 - 0.012ms returns 0x20000000
T1378 000:885.768 JLINK_ClrBPEx(BPHandle = 0x00000015)
T1378 000:885.774 - 0.008ms returns 0x00
T1378 000:885.781 JLINK_ReadReg(R0)
T1378 000:885.786 - 0.008ms returns 0xA4A91EFA
T1378 000:886.032 JLINK_HasError()
T1378 000:886.041 JLINK_WriteReg(R0, 0x00000003)
T1378 000:886.047 - 0.009ms returns 0
T1378 000:886.054 JLINK_WriteReg(R1, 0x08000000)
T1378 000:886.060 - 0.008ms returns 0
T1378 000:886.067 JLINK_WriteReg(R2, 0x00001620)
T1378 000:886.073 - 0.008ms returns 0
T1378 000:886.079 JLINK_WriteReg(R3, 0x04C11DB7)
T1378 000:886.085 - 0.008ms returns 0
T1378 000:886.091 JLINK_WriteReg(R4, 0x00000000)
T1378 000:886.097 - 0.008ms returns 0
T1378 000:886.103 JLINK_WriteReg(R5, 0x00000000)
T1378 000:886.109 - 0.008ms returns 0
T1378 000:886.115 JLINK_WriteReg(R6, 0x00000000)
T1378 000:886.121 - 0.008ms returns 0
T1378 000:886.127 JLINK_WriteReg(R7, 0x00000000)
T1378 000:886.133 - 0.007ms returns 0
T1378 000:886.139 JLINK_WriteReg(R8, 0x00000000)
T1378 000:886.144 - 0.008ms returns 0
T1378 000:886.151 JLINK_WriteReg(R9, 0x20000160)
T1378 000:886.156 - 0.008ms returns 0
T1378 000:886.162 JLINK_WriteReg(R10, 0x00000000)
T1378 000:886.168 - 0.008ms returns 0
T1378 000:886.174 JLINK_WriteReg(R11, 0x00000000)
T1378 000:886.180 - 0.008ms returns 0
T1378 000:886.186 JLINK_WriteReg(R12, 0x00000000)
T1378 000:886.191 - 0.008ms returns 0
T1378 000:886.198 JLINK_WriteReg(R13 (SP), 0x20001000)
T1378 000:886.204 - 0.008ms returns 0
T1378 000:886.210 JLINK_WriteReg(R14, 0x20000001)
T1378 000:886.215 - 0.008ms returns 0
T1378 000:886.222 JLINK_WriteReg(R15 (PC), 0x2000006A)
T1378 000:886.228 - 0.008ms returns 0
T1378 000:886.234 JLINK_WriteReg(XPSR, 0x01000000)
T1378 000:886.240 - 0.008ms returns 0
T1378 000:886.246 JLINK_WriteReg(MSP, 0x20001000)
T1378 000:886.252 - 0.008ms returns 0
T1378 000:886.258 JLINK_WriteReg(PSP, 0x20001000)
T1378 000:886.263 - 0.007ms returns 0
T1378 000:886.268 JLINK_WriteReg(CFBP, 0x00000000)
T1378 000:886.277 - 0.013ms returns 0
T1378 000:886.284 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T1378 000:886.288 - 0.006ms returns 0x00000016
T1378 000:886.293 JLINK_Go()
T1378 000:886.301   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:889.428 - 3.149ms
T1378 000:889.448 JLINK_IsHalted()
T1378 000:892.249   CPU_ReadMem(2 bytes @ 0x20000000)
T1378 000:892.685 - 3.247ms returns TRUE
T1378 000:892.702 JLINK_ReadReg(R15 (PC))
T1378 000:892.710 - 0.010ms returns 0x20000000
T1378 000:892.715 JLINK_ClrBPEx(BPHandle = 0x00000016)
T1378 000:892.719 - 0.006ms returns 0x00
T1378 000:892.724 JLINK_ReadReg(R0)
T1378 000:892.728 - 0.006ms returns 0x00000000
T1378 000:943.566 JLINK_WriteMemEx(0x20000000, 0x00000002 Bytes, Flags = 0x02000000)
T1378 000:943.603   Data:  FE E7
T1378 000:943.629   CPU_WriteMem(2 bytes @ 0x20000000)
T1378 000:944.093 - 0.534ms returns 0x2
T1378 000:944.109 JLINK_HasError()
T1378 000:953.791 JLINK_Close()
T1378 000:954.054   CPU_ReadMem(4 bytes @ 0xE0001000)
T1378 000:964.024 - 10.246ms
T1378 000:964.040   
T1378 000:964.046   Closed
