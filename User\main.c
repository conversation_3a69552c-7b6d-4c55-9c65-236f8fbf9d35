#include "stm32f10x.h"
#include "sys.h"
#include "delay.h"
#include "usart1.h"
#include "oled.h"
#include "PCA9685.h"
#include "TB6612.h"

void init (){
    delay_init(8);
 //   Usart1_Init(9600);
    OLED_Init();
 //   PCA9685_Init();
    MOTOR_Init();
}

void main (){

	init();

	// 等待系统稳定
	delay_ms(1000);
	OLED_ShowString(1,1,"PWM Test");

	// 测试软件PWM控制
	// 设置Motor1正转方向
	Motor_SetDirection(Motor1, 1);

	// 简单的PWM测试：逐步增加PWM值
	uint8_t current_pwm = 0;
	uint8_t pwm_direction = 1; // 1表示递增，0表示递减

	while(1){
		// 设置当前PWM值
		Motor_SetPWM(Motor1, current_pwm);

		// 在OLED上显示当前状态
		OLED_ShowString(1,1,"PWM Test");
		OLED_ShowString(2,1,"PWM:");
		OLED_ShowNum(2,5, current_pwm, 2);
		OLED_ShowString(2,8,"/19");

		// 显示PWM百分比
		uint8_t percentage = (current_pwm * 100) / 19;
		OLED_ShowString(3,1,"Duty:");
		OLED_ShowNum(3,6, percentage, 3);
		OLED_ShowString(3,9,"%");

		// 每500ms改变一次PWM值
		delay_ms(500);

		// 更新PWM值
		if (pwm_direction == 1) {
			// 递增模式
			current_pwm++;
			if (current_pwm >= 19) {
				pwm_direction = 0; // 切换到递减模式
			}
		} else {
			// 递减模式
			if (current_pwm > 0) {
				current_pwm--;
			}
			if (current_pwm == 0) {
				pwm_direction = 1; // 切换到递增模式
				// 在最低速时暂停1秒
				delay_ms(1000);
			}
		}
	}

}