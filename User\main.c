#include "stm32f10x.h"
#include "sys.h"
#include "delay.h"
#include "usart1.h"
#include "oled.h"
#include "PCA9685.h"
#include "TB6612.h"

void init (){
    delay_init(8);
 //   Usart1_Init(9600);
    OLED_Init();
 //   PCA9685_Init();
    MOTOR_Init();
}

void main (){

	init();

	// 测试PID电机控制
	// 注意：这只是一个测试示例，实际使用时请根据需要调用

	// 等待系统稳定
	//delay_ms(1000);
 //   OLED_ShowString(3,1,"NO");
	// 示例：让Motor1正转2圈
//	 MotorPID_GO(Motor1, -10.0f);
      Motor_SetPWM(Motor1,10);
	  Motor_SetDirection(Motor1, 1);
	// delay_ms(1000);
	// 示例：让Motor1反转1圈
	// MotorPID_GO(Motor1, -1.0f);
	// delay_ms(1000);
    // GPIO_SetBits(GPIOC, GPIO_Pin_4);
    // GPIO_ResetBits(GPIOC, GPIO_Pin_5);
	while(1){
		// 主循环
		// 在这里可以添加您的应用逻辑
		// 例如：根据传感器输入调用MotorPID_GO函数
        // Motor_SetPWM(Motor1, 99);
        // OLED_ShowString(2,1,"hello");
		// //delay_ms(100);
	}

}