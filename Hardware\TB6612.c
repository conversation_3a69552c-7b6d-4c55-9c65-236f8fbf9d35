#include "TB6612.h"
#include <math.h>
#include "delay.h"
#include "OLED.h"
#include <stdio.h>
#define TURNCAR 38

// 软件PWM相关变量
static uint8_t pwm_counter = 0;          // PWM计数器（0-99）
static uint8_t motor_pwm_values[4] = {0, 0, 0, 0}; // 四个电机的PWM值
static GPIO_TypeDef* pwm_ports[4] = {GPIOB, GPIOB, GPIOD, GPIOC}; // PWM引脚端口
static uint16_t pwm_pins[4] = {GPIO_Pin_0, GPIO_Pin_1, GPIO_Pin_14, GPIO_Pin_9}; // PWM引脚

/**************************************************************************
功能：TB6612初始化函数
入口参数：定时器参数
返回  值：无
**************************************************************************/
void MOTOR_Init()
{	OLED_ShowString(3,1,"NO1");
	TB6612_Init(19, 3599);  // 配置TIM8为1kHz中断频率 (72MHz/(19+1)/(3599+1) = 1kHz)
	motor_mode();
	// 软件PWM初始化
	Software_PWM_Init();
	// 初始化编码器
	Encoder_Init();
}

void TB6612_Init(int arr, int psc)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStrue;
	NVIC_InitTypeDef NVIC_InitStructure;
	
	// 使能所需的GPIO和定时器时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA | RCC_APB2Periph_GPIOB | RCC_APB2Periph_GPIOC | RCC_APB2Periph_GPIOD | RCC_APB2Periph_GPIOE, ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_AFIO, ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM8, ENABLE); // 只需要TIM8
	
	// 配置原PWM引脚为GPIO输出
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	
	// Motor1和Motor2的PWM引脚配置为GPIO输出 (PB0, PB1)
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0 | GPIO_Pin_1;
	GPIO_Init(GPIOB, &GPIO_InitStructure);
	
	// Motor3的PWM引脚配置为GPIO输出 (PD14)
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_14;
	GPIO_Init(GPIOD, &GPIO_InitStructure);
	
	// Motor4的PWM引脚配置为GPIO输出 (PC9)
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9;
	GPIO_Init(GPIOC, &GPIO_InitStructure);

	// 配置TIM8为软件PWM时基
	TIM_TimeBaseInitStrue.TIM_Period = arr;        // 19，产生20个计数步
	TIM_TimeBaseInitStrue.TIM_Prescaler = psc;     // 3599，产生1kHz中断频率
	TIM_TimeBaseInitStrue.TIM_CounterMode = TIM_CounterMode_Up;
	TIM_TimeBaseInitStrue.TIM_ClockDivision = TIM_CKD_DIV1;
	TIM_TimeBaseInit(TIM8, &TIM_TimeBaseInitStrue);

	// 配置TIM8中断
	TIM_ITConfig(TIM8, TIM_IT_Update, ENABLE);
	OLED_ShowString(3,4,"NO2");
	// 配置NVIC中断优先级
	NVIC_InitStructure.NVIC_IRQChannel = TIM8_UP_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_Init(&NVIC_InitStructure);
	OLED_ShowString(3,7,"NO3");
	// 使能定时器
	TIM_Cmd(TIM8, ENABLE);
}

/**************************************************************************
功能：软件PWM初始化
入口参数：无
返回  值：无
**************************************************************************/
void Software_PWM_Init(void)
{
	// 初始化软件PWM变量
	pwm_counter = 0;
	motor_pwm_values[0] = 0; // Motor1
	motor_pwm_values[1] = 0; // Motor2
	motor_pwm_values[2] = 0; // Motor3
	motor_pwm_values[3] = 0; // Motor4
	
	// 初始化所有PWM输出为低电平
	GPIO_ResetBits(GPIOB, GPIO_Pin_0); // Motor1
	GPIO_ResetBits(GPIOB, GPIO_Pin_1); // Motor2
	GPIO_ResetBits(GPIOD, GPIO_Pin_14); // Motor3
	GPIO_ResetBits(GPIOC, GPIO_Pin_9); // Motor4
}

/**************************************************************************
功能：TIM8中断服务程序 - 软件PWM实现
入口参数：无
返回  值：无
说明：每1kHz调用一次，实现50Hz的PWM输出，20级分辨率
**************************************************************************/
void TIM8_UP_IRQHandler(void)
{
	if (TIM_GetITStatus(TIM8, TIM_IT_Update) != RESET) {
		// 清除中断标志
		TIM_ClearITPendingBit(TIM8, TIM_IT_Update);
		
		// PWM计数器递增
		pwm_counter++;
		if (pwm_counter >= PWM_RESOLUTION) {
			pwm_counter = 0;
		}
		
		// 为每个电机生成PWM信号
		for (int i = 0; i < 4; i++) {
			if (pwm_counter < motor_pwm_values[i]) {
				// 设置为高电平
				GPIO_SetBits(pwm_ports[i], pwm_pins[i]);
			} else {
				// 设置为低电平
				GPIO_ResetBits(pwm_ports[i], pwm_pins[i]);
			}
            // OLED_ShowSignedNum(1,1,pwm_counter,5);
		}
	}
}

void motor_mode()
{
	GPIO_InitTypeDef GPIO_InitStructure;
	
	// 配置Motor1的方向控制引脚 - PC4和PC5
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC, ENABLE);
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4 | GPIO_Pin_5 | GPIO_Pin_8 | GPIO_Pin_9;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOC, &GPIO_InitStructure);
	
	// 配置Motor2的方向控制引脚 - PE7和PE8
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOE, ENABLE);
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_7 | GPIO_Pin_8;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOE, &GPIO_InitStructure);
	
	// 配置Motor3和Motor4的方向控制引脚 - PD10、PD11和PD15
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOD, ENABLE);
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10 | GPIO_Pin_11 | GPIO_Pin_15;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOD, &GPIO_InitStructure);
	
	// 初始状态设置
	/*
	M1 = 1;
	M2 = 1;
	M3 = 1;
	M4 = 1;
	M5 = 1; 
	M6 = 1;
	M7 = 1;
	M8 = 1;
	*/
}


/**************************************************************************
功能：编码器初始化函数
入口参数：无
返回  值：无
说明：仅使用TIM2作为编码器
**************************************************************************/
void Encoder_Init(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStrue;
	
	// 使能GPIO和定时器时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2, ENABLE);
	
	// 配置TIM2编码器引脚：PA0(TIM2_CH1)、PA1(TIM2_CH2)
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0 | GPIO_Pin_1;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING; // 编码器输入配置为浮空输入
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA, &GPIO_InitStructure);
	
	// 配置TIM2为编码器模式
	TIM_TimeBaseInitStrue.TIM_Period = 0xFFFF; // 16位最大计数值
	TIM_TimeBaseInitStrue.TIM_Prescaler = 0;   // 不分频
	TIM_TimeBaseInitStrue.TIM_CounterMode = TIM_CounterMode_Up;
	TIM_TimeBaseInitStrue.TIM_ClockDivision = TIM_CKD_DIV1;
	TIM_TimeBaseInit(TIM2, &TIM_TimeBaseInitStrue);
	
	// 设置TIM2为编码器接口模式
	TIM_EncoderInterfaceConfig(TIM2, TIM_EncoderMode_TI12,   TIM_ICPolarity_Rising, TIM_ICPolarity_Rising);
	
	// 清零计数器
	TIM_SetCounter(TIM2, 0);
	
	// 使能定时器
	TIM_Cmd(TIM2, ENABLE);
}

// 编码器累积计数静态变量
static int32_t accumulated_count = 0;      // 32位累积计数器
static uint16_t last_raw_count = 0;        // 上次原始读数存储
static uint8_t encoder_initialized = 0;    // 初始化标志

/**************************************************************************
功能：获取编码器累积计数值
入口参数：无
返回  值：编码器累积计数值（有符号，支持正负方向，解决边界抖动问题）
说明：使用差值累积法，避免16位计数器回绕时的边界跳跃问题
**************************************************************************/
int32_t Get_Encoder_Count(void)
{
	// 获取当前16位原始计数值
	uint16_t current_raw = TIM_GetCounter(TIM2);
	
	// 首次调用时进行初始化
	if (!encoder_initialized) {
		last_raw_count = current_raw;
		accumulated_count = 0;
		encoder_initialized = 1;
		return accumulated_count;
	}
	
	// 计算与上次读数的16位有符号差值
	// 这样可以正确处理计数器回绕：
	// 例：从65535到0，差值为(int16_t)(0-65535) = 1
	// 例：从0到65535，差值为(int16_t)(65535-0) = -1
	int16_t diff = (int16_t)(current_raw - last_raw_count);
	
	// 更新累积计数器
	accumulated_count += diff;
	
	// 保存当前读数为下次的 上次读数
	last_raw_count = current_raw;
	
	return accumulated_count;
}

/**************************************************************************
功能：重置编码器计数
入口参数：无
返回  值：无
说明：将TIM2计数器和累积计数状态同时清零
**************************************************************************/
void Reset_Encoder(void)
{
	// 重置硬件计数器
	TIM_SetCounter(TIM2, 0);
	
	// 重置软件累积状态
	accumulated_count = 0;
	last_raw_count = 0;
	encoder_initialized = 0;
}

/**************************************************************************
功能：PID控制器初始化
入口参数：PID控制器指针，Kp, Ki, Kd参数
返回  值：无
**************************************************************************/
void PID_Init(PID_Controller_t *pid, float kp, float ki, float kd)
{
    pid->Kp = kp;
    pid->Ki = ki;
    pid->Kd = kd;
    pid->target = 0.0f;
    pid->current = 0.0f;
    pid->error = 0.0f;
    pid->last_error = 0.0f;
    pid->integral = 0.0f;
    pid->derivative = 0.0f;
    pid->output = 0.0f;
    pid->max_output = (float)PWM_MAX_VALUE;   // PWM最大值
    pid->min_output = -(float)PWM_MAX_VALUE;  // PWM最小值
    pid->max_integral = 20.0f; // 积分限幅
}

/**************************************************************************
功能：PID控制计算
入口参数：PID控制器指针，目标值，当前值
返回  值：PID输出值
**************************************************************************/
float PID_Calculate(PID_Controller_t *pid, float target, float current)
{
    pid->target = target;
    pid->current = current;

    // 计算误差
    pid->error = pid->target - pid->current;

    // 积分项计算（带限幅）
    pid->integral += pid->error;
    if (pid->integral > pid->max_integral) {
        pid->integral = pid->max_integral;
    } else if (pid->integral < -pid->max_integral) {
        pid->integral = -pid->max_integral;
    }

    // 微分项计算
    pid->derivative = pid->error - pid->last_error;

    // PID输出计算
    pid->output = pid->Kp * pid->error +
                  pid->Ki * pid->integral +
                  pid->Kd * pid->derivative;

    // 输出限幅
    if (pid->output > pid->max_output) {
        pid->output = pid->max_output;
    } else if (pid->output < pid->min_output) {
        pid->output = pid->min_output;
    }

    // 保存当前误差为下次的上次误差
    pid->last_error = pid->error;

    return pid->output;
}

/**************************************************************************
功能：设置指定电机的方向
入口参数：电机编号，方向（1正转，-1反转，0停止）
返回  值：无
**************************************************************************/
void Motor_SetDirection(MotorNum_t motor, int direction)
{
    switch(motor) {
        case Motor1:
            if (direction > 0) {
                // Motor1正转
                GPIO_SetBits(GPIOC, GPIO_Pin_4);
                GPIO_ResetBits(GPIOC, GPIO_Pin_5);
            } else if (direction < 0) {
                // Motor1反转
                GPIO_ResetBits(GPIOC, GPIO_Pin_4);
                GPIO_SetBits(GPIOC, GPIO_Pin_5);
            } else {
                // Motor1停止
                GPIO_ResetBits(GPIOC, GPIO_Pin_4);
                GPIO_ResetBits(GPIOC, GPIO_Pin_5);
            }
            break;

        case Motor2:
            if (direction > 0) {
                // Motor2正转
                GPIO_SetBits(GPIOE, GPIO_Pin_7);
                GPIO_ResetBits(GPIOE, GPIO_Pin_8);
            } else if (direction < 0) {
                // Motor2反转
                GPIO_ResetBits(GPIOE, GPIO_Pin_7);
                GPIO_SetBits(GPIOE, GPIO_Pin_8);
            } else {
                // Motor2停止
                GPIO_ResetBits(GPIOE, GPIO_Pin_7);
                GPIO_ResetBits(GPIOE, GPIO_Pin_8);
            }
            break;

        case Motor3:
            if (direction > 0) {
                // Motor3正转
                GPIO_SetBits(GPIOD, GPIO_Pin_10);
                GPIO_ResetBits(GPIOD, GPIO_Pin_11);
            } else if (direction < 0) {
                // Motor3反转
                GPIO_ResetBits(GPIOD, GPIO_Pin_10);
                GPIO_SetBits(GPIOD, GPIO_Pin_11);
            } else {
                // Motor3停止
                GPIO_ResetBits(GPIOD, GPIO_Pin_10);
                GPIO_ResetBits(GPIOD, GPIO_Pin_11);
            }
            break;

        case Motor4:
            if (direction > 0) {
                // Motor4正转
                GPIO_SetBits(GPIOD, GPIO_Pin_15);
                GPIO_ResetBits(GPIOC, GPIO_Pin_8);
            } else if (direction < 0) {
                // Motor4反转
                GPIO_ResetBits(GPIOD, GPIO_Pin_15);
                GPIO_SetBits(GPIOC, GPIO_Pin_8);
            } else {
                // Motor4停止
                GPIO_ResetBits(GPIOD, GPIO_Pin_15);
                GPIO_ResetBits(GPIOC, GPIO_Pin_8);
            }
            break;
    }
}

/**************************************************************************
功能：设置指定电机的PWM值
入口参数：电机编号，PWM值（0-19）
返回  值：无
**************************************************************************/
void Motor_SetPWM(MotorNum_t motor, int pwm)
{
    // 限制PWM值的绝对值范围
    if (pwm > PWM_MAX_VALUE) {
        pwm = PWM_MAX_VALUE;
    } else if (pwm < 0) {
        pwm = 0; // 不允许负值，方向由SetDirection控制
    }

    switch(motor) {
        case Motor1:
            motor_pwm_values[0] = pwm;
            break;
        case Motor2:
            motor_pwm_values[1] = pwm;
            break;
        case Motor3:
            motor_pwm_values[2] = pwm;
            break;
        case Motor4:
            motor_pwm_values[3] = pwm;
            break;
    }
}

void Move_stop(void)
{
    Motor_SetDirection(Motor1, 0);
    Motor_SetDirection(Motor2, 0);
    Motor_SetDirection(Motor3, 0);
    Motor_SetDirection(Motor4, 0);
}

/**************************************************************************
功能：电机位置环PID控制，控制电机转动指定圈数
入口参数：电机编号，转动圈数（正数正转，负数反转）
返回  值：无
说明：使用编码器反馈进行位置控制，阻塞式执行直到到达目标位置
**************************************************************************/
void MotorPID_GO(MotorNum_t motor, float revolutions)
{
    PID_Controller_t pid;
    float target_counts;
    float current_counts;
    float pid_output;
    int direction;
    int pwm_value;
	int times_comfirm = 0;
    uint32_t timeout_counter = 0;
    const uint32_t MAX_TIMEOUT = 100000; // 超时计数器，防止死循环

    // 初始化PID控制器
    // 这些参数需要根据实际电机特性进行调整
    PID_Init(&pid, 0.01f, 0.00f, 0.01f); 

    // 计算目标编码器计数值
    target_counts = revolutions * ENCODER_COUNTS_PER_REVOLUTION;
	OLED_ShowSignedNum(1,9,target_counts,5);
    // 重置编码器
    Reset_Encoder();

    // 停止电机，确保初始状态
    Move_stop();
    delay_ms(10);

    // PID控制循环
    while (timeout_counter < MAX_TIMEOUT) {
        // 获取当前编码器计数
        current_counts = (float)Get_Encoder_Count();
		OLED_ShowSignedNum(1,1,current_counts,5);
        // 计算PID输出
        pid_output = PID_Calculate(&pid, target_counts, current_counts);
		OLED_ShowSignedNum(2,1,pid_output,5);
        // 判断是否到达目标位置（允许一定误差）
        if (fabs(pid.error) <= 5.0f) {  // 误差小于5个编码器计数认为到达
			times_comfirm++ ;
			if (times_comfirm>=1) break;//5次确认到达目标位置
        }
		
        // 根据PID输出确定方向和PWM值
        if (pid_output > 0) {
            direction = 1;  // 正转
            pwm_value = (int)pid_output;
        } else if (pid_output < 0) {
            direction = -1; // 反转
            pwm_value = (int)(-pid_output);
        } else {
            direction = 0;  // 停止
            pwm_value = 0;
        }

        // 设置电机方向和PWM
        Motor_SetDirection(motor, direction);
		OLED_ShowSignedNum(2,9,pwm_value,5);
        Motor_SetPWM(motor, pwm_value);

        // 短暂延时，控制循环频率
        delay_ms(5);
        timeout_counter++;
		
    }

    // 停止电机
    Move_stop();

    // 如果超时，可以通过OLED显示调试信息
    if (timeout_counter >= MAX_TIMEOUT) {
        // 超时处理 - 在这里添加错误处理代码
    }
}

